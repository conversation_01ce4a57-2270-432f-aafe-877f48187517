"use client";

import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  Bell,
  ChevronDown,
  CircleChevronLeft,
  LucideProps,
  User,
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import React from "react";
import { Locale, usePathname, useRouter } from "@/i18n/navigation";
import { ROUTE } from "@/constants/route";
import { useTranslations } from "next-intl";
import DrawerDashboard from "@/components/common/drawer-sidebar";
import { cn } from "@/lib/utils";
import { useAuthWithRouter } from "@/hooks/use-auth-with-router";
import { useResume } from "@/lib/store/resume-store";

interface HeaderProfileProps {
  sidebar: (
    | {
        name: string;
        icon: React.ForwardRefExoticComponent<
          Omit<LucideProps, "ref"> & React.RefAttributes<SVGSVGElement>
        >;
        href: string;
        items: undefined;
      }
    | {
        name: string;
        icon: React.ForwardRefExoticComponent<
          Omit<LucideProps, "ref"> & React.RefAttributes<SVGSVGElement>
        >;
        href: undefined;
        items: { name: string; href: string }[];
      }
  )[];
}

const HeaderProfile: React.FC<HeaderProfileProps> = ({ sidebar }) => {
  return (
    <div className="sticky top-0 z-50 min-h-16 px-6 min-[1024px]:px-8 py-3 bg-background flex items-center justify-between min-[1024px]:justify-end">
      <DrawerDashboard sidebar={sidebar} />
      <p className="absolute min-[1024px]:hidden text-main font-bold text-[28px] cursor-pointer left-1/2 -translate-x-1/2">
        EasyHire
      </p>
      <ProfileName />
    </div>
  );
};

export default HeaderProfile;

const ProfileName = () => {
  const { header, onBack } = useResume();
  const router = useRouter();
  const pathname = usePathname();

  const { user, logoutWithNavigation } = useAuthWithRouter();

  const t = useTranslations();

  const handleChangeLanguage = (newLocale: Locale) => {
    router.replace({ pathname }, { locale: newLocale });
  };

  return (
    <div
      className={cn(
        "w-full flex items-center justify-end min-[1024px]:justify-between gap-4",
        !header.fileName ? "!justify-end" : ""
      )}
    >
      <div
        className={cn(
          "hidden flex-col min-[1024px]:flex",
          !header.fileName ? "!hidden" : ""
        )}
      >
        <div
          className="flex gap-1 items-center cursor-pointer w-fit"
          onClick={onBack}
        >
          <CircleChevronLeft fill="#667085" color="#fff" />
          <p className="text-base text-[#667085]">Back to scan</p>
        </div>

        <h4 className="text-xl font-semibold text-[#06042B]">
          {header.fileName}
        </h4>
      </div>

      <div className="flex items-center gap-4 min-[1024px]:gap-9">
        <Bell fill="#667085" color="#667085" />
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <div className="flex items-center gap-2 cursor-pointer">
              <Avatar className="w-8 h-8">
                <AvatarImage
                  src={user?.photoURL || ""}
                  alt={user?.displayName || "User"}
                />
                <AvatarFallback>
                  <User className="w-8 h-8" />
                </AvatarFallback>
              </Avatar>

              <p className="hidden md:block font-medium text-sm text-[#0F172A]">
                {user?.displayName || ""}
              </p>

              <ChevronDown className="hidden md:block" />
            </div>
          </DropdownMenuTrigger>
          <DropdownMenuContent className="w-44">
            <DropdownMenuLabel>Languages</DropdownMenuLabel>
            <DropdownMenuSeparator />
            <DropdownMenuGroup>
              <DropdownMenuItem onClick={() => handleChangeLanguage("vi")}>
                Vietnamese
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleChangeLanguage("en")}>
                English
              </DropdownMenuItem>
            </DropdownMenuGroup>
            <DropdownMenuSeparator />
            <DropdownMenuItem onClick={logoutWithNavigation}>
              {t("common.logout")}
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </div>
  );
};
