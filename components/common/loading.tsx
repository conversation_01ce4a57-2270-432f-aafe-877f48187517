import React, { PropsWithChildren, useState } from "react";
import { useDebounce } from "react-use";
import { Button } from "../ui/button";

interface LoadingProps extends PropsWithChildren {
  messages?: string[];
  onCancel?: () => void;
}

export const SpinnerLoading = () => {
  return (
    <div className="relative w-32 h-32">
      <div className="absolute w-full h-full animate-[step-rotate_3s_cubic-bezier(0.3,0.85,0.75,1.00)_infinite]">
        <div className="absolute w-10 h-10 rounded-full bg-main top-0 left-1/2 -translate-x-1/2"></div>
        <div className="absolute w-10 h-10 rounded-full bg-main left-0 top-1/2 -translate-y-1/2"></div>
        <div className="absolute w-10 h-10 rounded-full bg-main bottom-0 left-1/2 -translate-x-1/2"></div>
        <div className="absolute w-10 h-10 rounded-full bg-main right-0 top-1/2 -translate-y-1/2"></div>
      </div>

      <style jsx>{`
        @keyframes step-rotate {
          0% {
            transform: rotate(0deg);
          }
          30% {
            transform: rotate(180deg);
          }
          50% {
            transform: rotate(180deg);
          }
          80% {
            transform: rotate(360deg);
          }
          100% {
            transform: rotate(360deg);
          }
        }
      `}</style>
    </div>
  );
};

export const LoadingOverlay: React.FC<LoadingProps> = ({
  children,
  messages,
  onCancel,
}) => {
  const [loadingMessage, setLoadingMessage] = useState<string>(
    messages?.length ? messages[0] : ""
  );

  useDebounce(
    () => {
      if (messages && messages.length) {
        let message = "";

        const messageIndex = messages.findIndex(
          (message) => message === loadingMessage
        );
        if (messageIndex === -1 || messageIndex === messages.length - 1) {
          message = messages[0];
        } else {
          message = messages[messageIndex + 1];
        }
        setLoadingMessage(message);
      }
    },
    3000,
    [loadingMessage]
  );

  return (
    <div className="fixed inset-0 z-50 bg-background/20 backdrop-blur-sm flex flex-col gap-6 items-center justify-center">
      <SpinnerLoading />
      {loadingMessage && (
        <p className="text-xl text-center text-primary-500">{loadingMessage}</p>
      )}
      {onCancel && (
        <Button
          variant="outline"
          onClick={onCancel}
          className="px-6 py-2 rounded-md font-medium transition-colors"
        >
          Cancel
        </Button>
      )}
      {children}
    </div>
  );
};
