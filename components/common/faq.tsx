"use client";

import React, { useState } from "react";
import {
  Accordion,
  Accordion<PERSON>ontent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";

import PlusIcon from "../../public/images/faq/plus-circle.svg";
import MinusIcon from "../../public/images/faq/minus-circle.svg";
import Image from "next/image";

const DATA = [
  {
    question: "How does the AI generate questions?",
    answer:
      "We use natural language processing to analyze both your resume and the job description. Our AI then selects questions that align with your skills, experience, and the role’s requirements. Each question feels realistic and directly relevant to the position you’re targeting.",
  },
  {
    question: "Does the tool work for both technical and non-technical roles?",
    answer:
      "We use natural language processing to analyze both your resume and the job description. Our AI then selects questions that align with your skills, experience, and the role’s requirements. Each question feels realistic and directly relevant to the position you’re targeting.",
  },
  {
    question: "Can I practice with voice, video, or text?",
    answer:
      "We use natural language processing to analyze both your resume and the job description. Our AI then selects questions that align with your skills, experience, and the role’s requirements. Each question feels realistic and directly relevant to the position you’re targeting.",
  },
  {
    question: "When will I receive feedback?",
    answer:
      "We use natural language processing to analyze both your resume and the job description. Our AI then selects questions that align with your skills, experience, and the role’s requirements. Each question feels realistic and directly relevant to the position you’re targeting.",
  },
  {
    question: "What makes this different from generic mock interview tools?",
    answer:
      "We use natural language processing to analyze both your resume and the job description. Our AI then selects questions that align with your skills, experience, and the role’s requirements. Each question feels realistic and directly relevant to the position you’re targeting.",
  },
];

const FAQ = () => {
  const [values, setValues] = useState<string[]>([]);
  return (
    <div className="container py-8 md:py-12 flex flex-col gap-2 md:gap-6">
      <p className="text-[24px] md:text-[40px] leading-[1.2] font-bold text-gray-900 text-center">
        Frequently Asked Questions
      </p>
      <Accordion
        type="multiple"
        className="w-full"
        value={values}
        onValueChange={(value) => setValues(value)}
      >
        {DATA.map(({ question, answer }) => (
          <AccordionItem
            key={question}
            value={question}
            className="pt-6 pb-8 flex flex-col gap-3 border-gray-100"
          >
            <AccordionTrigger className="p-0 hover:no-underline flex items-start justify-between gap-4">
              <p className="text-[16px] md:text-[20px] leading-[1.5] text-gray-900 text-left">
                {question}
              </p>
              <Image
                alt=""
                src={values.includes(question) ? MinusIcon : PlusIcon}
                width={24}
                height={24}
              />
            </AccordionTrigger>
            <AccordionContent className="text-[14px] md:text-[16px] leading-[1.5] text-gray-600 p-0">
              {answer}
            </AccordionContent>
          </AccordionItem>
        ))}
      </Accordion>
    </div>
  );
};

export default FAQ;
