"use client";

import { inter } from "@/app/fonts";
import {
  Sidebar,
  SidebarContent,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubItem,
  SidebarTrigger,
  useSidebar,
} from "@/components/ui/sidebar";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "../ui/collapsible";
import { cn } from "@/lib/utils";
import { LucideProps } from "lucide-react";
import { Link } from "@/i18n/navigation";
import {
  ForwardRefExoticComponent,
  RefAttributes,
  useState,
  useEffect,
} from "react";
import { usePathname } from "@/i18n/navigation";

interface AppSidebarProps {
  sidebar: (
    // | {
    //     name: string;
    //     icon: ForwardRefExoticComponent<
    //       Omit<LucideProps, "ref"> & RefAttributes<SVGSVGElement>
    //     >;
    //     href: string;
    //     items: undefined;
    //   }
    // | 
    {
        name: string;
        icon: ForwardRefExoticComponent<
          Omit<LucideProps, "ref"> & RefAttributes<SVGSVGElement>
        >;
        href: undefined;
        items: { name: string; href: string }[];
      }
  )[];
}

export function AppSidebar({ sidebar }: AppSidebarProps) {
  const { state } = useSidebar();
  const [selected, setSelected] = useState<string>("");
  const pathname = usePathname();

  useEffect(() => {
    for (const item of sidebar) {
      if (item.href && pathname === item.href) {
        setSelected(item.name);
        return;
      }
      if (item.items) {
        for (const subItem of item.items) {
          if (pathname === subItem.href) {
            setSelected(subItem.name);
            return;
          }
        }
      }
    }
  }, [pathname]);

  return (
    <Sidebar
      collapsible="icon"
      className="hidden md:hidden min-[1024px]:block shadow-[0px_4px_16px_0px_rgba(0,0,0,0.12)]"
    >
      <SidebarHeader className={cn(state === "expanded" ? "px-5" : "px-2")}>
        <div
          className={cn(
            "flex flex-row justify-between items-center border-b-[1px] border-b-gray-300 py-6"
          )}
        >
          {state === "expanded" ? (
            <p
              className={`text-main font-bold text-xl leading-none cursor-pointer ${inter.className}`}
            >
              EasyHire
            </p>
          ) : null}
          <SidebarTrigger />
        </div>
      </SidebarHeader>
      <SidebarContent
        className={cn("px-5", state === "expanded" ? "block" : "hidden")}
      >
        <SidebarMenu>
          <SidebarMenu className="flex flex-col gap-2">
            {sidebar.map((item) =>
              item.items?.length ? (
                <Collapsible
                  open={
                    selected === item.name ||
                    item.items.map((item) => item.name).includes(selected)
                  }
                  className="group/collapsible"
                  key={item.name}
                >
                  <SidebarMenuItem>
                    <CollapsibleTrigger asChild>
                      <SidebarMenuButton
                        className={cn(
                          "hover:!bg-[#EDECFE] hover:!text-main",
                          selected === item.name ||
                            item.items
                              .map((item) => item.name)
                              .includes(selected)
                            ? "text-main"
                            : ""
                        )}
                        onClick={() => setSelected(item.name)}
                      >
                        <span>
                          <item.icon
                            className={cn(
                              selected === item.name ||
                                item.items
                                  .map((item) => item.name)
                                  .includes(selected)
                                ? "fill-main text-background"
                                : ""
                            )}
                          />
                        </span>
                        {item.name}
                      </SidebarMenuButton>
                    </CollapsibleTrigger>
                    <CollapsibleContent>
                      <SidebarMenuSub className="border-none px-0 mx-0 pl-8">
                        {item.items.map((subItem) => (
                          <Link key={subItem.name} href={subItem.href}>
                            <SidebarMenuSubItem>
                              <SidebarMenuButton
                                className={cn(
                                  "hover:bg-[#EDECFE] hover:text-main",
                                  selected === subItem.name
                                    ? "text-main bg-[#EDECFE]"
                                    : ""
                                )}
                                onClick={() => setSelected(subItem.name)}
                              >
                                {subItem.name}
                              </SidebarMenuButton>
                            </SidebarMenuSubItem>
                          </Link>
                        ))}
                      </SidebarMenuSub>
                    </CollapsibleContent>
                  </SidebarMenuItem>
                </Collapsible>
              ) : (
                <SidebarMenuItem key={item.name}>
                  <Link href={item?.href || ""}>
                    <SidebarMenuButton
                      onClick={() => setSelected(item.name)}
                      className={cn(
                        "hover:!bg-[#EDECFE] hover:!text-main",
                        selected === item.name ? "text-main bg-[#EDECFE]" : ""
                      )}
                    >
                      <item.icon
                        className={cn(
                          selected === item.name
                            ? "fill-main text-background"
                            : ""
                        )}
                      />
                      {item.name}
                    </SidebarMenuButton>
                  </Link>
                </SidebarMenuItem>
              )
            )}
          </SidebarMenu>
        </SidebarMenu>
      </SidebarContent>
    </Sidebar>
  );
}
