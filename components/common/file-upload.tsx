"use client";

import { cn } from "@/lib/utils";
import { FileUp, X } from "lucide-react";
import React, { ChangeEvent, useCallback, useEffect, useState } from "react";
import { useMount } from "react-use";

interface FileUploadProps {
  file: File | null;
  onFileSelect: (file: File | null) => void;
  disabled?: boolean;
  title?: string;
}

const FileUpload: React.FC<FileUploadProps> = ({
  file,
  onFileSelect,
  disabled,
  title,
}) => {
  const [fileInfo, setFileInfo] = useState<{
    name: string;
    size: string;
  } | null>(null);
  const [isDragging, setIsDragging] = useState(false);

  const handleFileChange = (e: ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      handleFile(file);
    }
  };

  const handleFile = (file: File) => {
    const size =
      file.size < 1024 * 1024
        ? `${(file.size / 1024).toFixed(2)} KB`
        : `${(file.size / (1024 * 1024)).toFixed(2)} MB`;

    setFileInfo({
      name: file.name,
      size: size,
    });

    onFileSelect(file);
  };

  const handleDeleteFile = (e: React.MouseEvent) => {
    e.preventDefault();

    setFileInfo(null);
    onFileSelect?.(null);
  };

  const handleDragEnter = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);
  }, []);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);

    const files = e.dataTransfer.files;
    if (files.length > 0) {
      handleFile(files[0]);
    }
  }, []);

  //
  //
  //

  useEffect(() => {
    if (file) {
      const size =
        file.size < 1024 * 1024
          ? `${(file.size / 1024).toFixed(2)} KB`
          : `${(file.size / (1024 * 1024)).toFixed(2)} MB`;

      setFileInfo({
        name: file.name,
        size: size,
      });
    } else {
      setFileInfo(null);
    }
  }, [file]);

  return (
    <div className="w-full">
      <div
        className={cn(
          "relative border-2 h-auto border-dashed rounded-lg transition-colors flex flex-col justify-center px-4 py-2",
          isDragging
            ? "border-main bg-main/5"
            : fileInfo
            ? "border-main"
            : "border-[#667085]",
          disabled ? "opacity-40 cursor-no-drop" : ""
        )}
        onDragEnter={handleDragEnter}
        onDragLeave={handleDragLeave}
        onDragOver={handleDragOver}
        onDrop={handleDrop}
      >
        <input
          type="file"
          className="hidden"
          onChange={handleFileChange}
          id="file-input"
          disabled={disabled}
        />

        <label
          htmlFor="file-input"
          className={cn(
            "block w-full cursor-pointer",
            disabled ? "pointer-events-none" : ""
          )}
        >
          <div
            className={cn(
              "flex flex-col md:flex-row justify-between items-center gap-1",
              fileInfo ? "flex-row" : "",
              disabled ? "cursor-no-drop" : ""
            )}
          >
            {fileInfo ? (
              <>
                <p className="text-base text-main truncate overflow-hidden">
                  {fileInfo.name}
                </p>
                <button
                  type="button"
                  onClick={(e) => handleDeleteFile(e)}
                  className="p-1 hover:bg-gray-100 rounded-full transition-colors"
                >
                  <X color="#D8D8DA" />
                </button>
              </>
            ) : (
              <>
                <div className="flex items-center gap-1">
                  <FileUp
                    size={24}
                    fill={isDragging ? "#7E77F8" : "#667085"}
                    color="#fff"
                  />
                  <p
                    className={cn(
                      "text-base",
                      isDragging ? "text-main" : "text-[#06042B]"
                    )}
                  >
                    {title || "Drag and drop resume file here"}
                  </p>
                </div>

                <div className="flex flex-col md:flex-row items-center gap-1 md:gap-4">
                  <p className="text-sm text-[#667085]">
                    Supported format: .pdf, .doc
                  </p>

                  <p className="text-sm text-[#667085]">
                    Maximum file size: 2MB
                  </p>
                </div>
              </>
            )}
          </div>
        </label>
      </div>
    </div>
  );
};

export default FileUpload;
