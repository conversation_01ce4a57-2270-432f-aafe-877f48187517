"use client";

import { usePathname } from "next/navigation";
import AppLayout from "./AppLayout";

interface LayoutRouterProps {
  children: React.ReactNode;
  locale: string;
}

export default function LayoutRouter({ children, locale }: LayoutRouterProps) {
  const pathname = usePathname();

  // Check if current path is an auth route
  const isAuthRoute =
    pathname.startsWith("/auth") || pathname.startsWith(`/${locale}/auth`);

  // Render different layouts based on route
  if (isAuthRoute) {
    return <>{children}</>;
  }

  return <AppLayout>{children}</AppLayout>;
}
