"use client";

import { AppSidebar } from "@/components/common/app-sidebar";
import HeaderProfile from "@/components/common/header-profile";
import { SidebarProvider } from "@/components/ui/sidebar";
import { ROUTE } from "@/constants/route";
import { File, Home } from "lucide-react";

const SIDEBAR = [
  // { name: "Dashboard", icon: Home, href: ROUTE.HOME },
  {
    name: "AI Resume Mate",
    icon: File,
    href: undefined,
    items: [
      { name: "Resume Management", href: ROUTE.RESUME_MANAGEMENT },
      { name: "AI Resume Score", href: ROUTE.RESUME_SCORE },
      { name: "AI Optimize Resume", href: ROUTE.OPTIMIZE_RESUME },
      //   { name: "Build Resume", href: ROUTE.RESUME_BUILDER_PAGE },
    ],
  },
];

export default function AppLayout({ children }: { children: React.ReactNode }) {
  return (
    <SidebarProvider>
      <AppSidebar sidebar={SIDEBAR} />
      <main className="flex flex-col flex-1 h-screen overflow-x-hidden">
        <HeaderProfile sidebar={SIDEBAR} />
        <div className="bg-[#edecfe] md:bg-[#f9f9ff] flex-1 w-full relative">
          {children}
        </div>
      </main>
    </SidebarProvider>
  );
}
