import Image from "next/image";
import React, { PropsWithChildren } from "react";
import AuthImage from "../../../public/images/auth.png";

const AuthLayout: React.FC<PropsWithChildren> = ({ children }) => {
  return (
    <div className="overflow-hidden h-screen w-screen flex justify-center items-center bg-[#f9f9ff]">
      <div className="hidden lg:block h-full w-1/2">
        <Image alt="" src={AuthImage} className="h-full" />
      </div>
      <div className="flex-1 h-full flex flex-col justify-center items-center p-4">
        {children}
      </div>
    </div>
  );
};

export default AuthLayout;
