import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>Trigger,
} from "@/components/ui/dialog";
import { cn } from "@/lib/utils";
import { DialogClose } from "@radix-ui/react-dialog";
import { FileUp, Loader2, X } from "lucide-react";
import { RadioGroup, RadioGroupItem } from "../ui/radio-group";
import { getAllResumes } from "@/apis/resume/resume";
import { useQuery } from "@tanstack/react-query";
import { ResponseUploadResume } from "@/apis/resume/types";
import { useState } from "react";

const SelectResumeItem: React.FC<{
  resume: ResponseUploadResume;
  onClick: (resume: ResponseUploadResume | null) => void;
  selected?: boolean;
}> = ({ resume, onClick, selected }) => {
  return (
    <div
      className={cn(
        "flex flex-row cursor-pointer justify-between items-center px-5 py-4 rounded-lg border hover:border-main",
        selected ? "border-main" : "border-[#E8E8E8]"
      )}
      onClick={() => onClick(resume)}
    >
      <p className={cn("text-sm text-[#06042B] font-semibold")}>
        {resume.resume_name}
      </p>
      {selected && (
        <RadioGroup>
          <RadioGroupItem value="" checked={true} />
        </RadioGroup>
      )}
    </div>
  );
};

interface DialogSelectResumeProps {
  selected: ResponseUploadResume | null;
  onSelectFile: (resume: ResponseUploadResume | null) => void;
}

const DialogSelectResume: React.FC<DialogSelectResumeProps> = ({
  selected,
  onSelectFile,
}) => {
  const { data: allResumes, isLoading } = useQuery({
    queryKey: ["get-all-resume"],
    queryFn: getAllResumes,
  });

  const [selectResume, setSelectResume] = useState<ResponseUploadResume | null>(
    selected
  );

  const [open, setOpen] = useState<boolean>(false);

  return (
    <Dialog open={open} onOpenChange={(open) => setOpen(open)}>
      <DialogTrigger asChild>
        <div
          className={cn(
            "relative cursor-pointer border-2 h-auto border-dashed rounded-lg transition-colors flex flex-col justify-center px-4 py-2",
            selected ? "border-main" : "border-[#667085]"
          )}
        >
          <div
            className={cn(
              "flex flex-col md:flex-row justify-between items-center gap-1 w-full",
              selected ? "flex-row" : ""
            )}
          >
            {selected ? (
              <>
                <p className="text-base text-main truncate overflow-hidden">
                  {selected.resume_name}
                </p>
                <button
                  type="button"
                  onClick={(e) => {
                    e.preventDefault();
                    setSelectResume(null);
                    onSelectFile(null);
                  }}
                  className="p-1 hover:bg-gray-100 rounded-full transition-colors"
                >
                  <X color="#D8D8DA" className="flex-shrink-0" />
                </button>
              </>
            ) : (
              <>
                <div className="flex items-center gap-1">
                  <FileUp size={24} fill="#667085" color="#fff" />
                  <p className="text-base text-[#06042B] font-medium">
                    Select Resume
                  </p>
                </div>
                <p className="text-sm text-[#667085]">
                  Select from the existing library
                </p>
              </>
            )}
          </div>
        </div>
      </DialogTrigger>
      <DialogContent className="p-0 rounded-lg gap-0">
        <DialogHeader className="border-b border-b-[#E9E9EB]">
          <div className="flex items-center justify-between px-6 py-4">
            <DialogTitle className="text-[#667085] text-lg">
              Select a resume
            </DialogTitle>
            <DialogClose className="rounded-sm transition-opacity hover:opacity-100 focus:outline-none disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground">
              <X className="h-5 w-5" color="#D8D8DA" />
              <span className="sr-only">Close</span>
            </DialogClose>
          </div>
        </DialogHeader>
        <div className="relative z-0 grid gap-4 px-3 py-5 md:py-8">
          <div className="flex flex-col md:flex-row px-3">
            <p className="text-xl font-semibold text-[#06042B] gap-3">
              My Resume
            </p>
          </div>

          <div className="max-h-[350px] flex flex-col gap-4 overflow-auto px-3 pb-14 no-scrollbar">
            {isLoading && (
              <div className="w-full flex justify-center">
                <Loader2 size={60} className="animate-spin" color="#7E77F8" />
              </div>
            )}
            {allResumes?.length &&
              allResumes.map((resume) => (
                <SelectResumeItem
                  resume={resume}
                  onClick={(resume) => setSelectResume(resume)}
                  selected={
                    selectResume ? selectResume.id === resume.id : false
                  }
                />
              ))}
          </div>
        </div>
        <DialogFooter className="bg-background fixed z-50 bottom-0 left-0 right-0 px-6 py-4 border-t border-t-[#E9E9EB] flex flex-row justify-end">
          <Button
            className="text-sm text-background font-normal bg-main hover:bg-main py-2.5 px-[30px] w-fit"
            onClick={() => {
              if (selectResume) {
                onSelectFile(selectResume);
                setOpen(false);
              }
            }}
          >
            Continue
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default DialogSelectResume;
