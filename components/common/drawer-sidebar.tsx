"use client";

import * as React from "react";
import { AlignJustify, ChevronDown, LucideProps, X } from "lucide-react";

import {
  Drawer,
  DrawerClose,
  DrawerContent,
  DrawerTrigger,
  useDrawer,
} from "@/components/ui/drawer";
import { inter } from "@/app/fonts";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "../ui/collapsible";
import { cn } from "@/lib/utils";
import { Link } from "@/i18n/navigation";
import { ROUTE } from "@/constants/route";

interface DrawerDashboardProps {
  sidebar: (
    | {
        name: string;
        icon: React.ForwardRefExoticComponent<
          Omit<LucideProps, "ref"> & React.RefAttributes<SVGSVGElement>
        >;
        href: string;
        items: undefined;
      }
    | {
        name: string;
        icon: React.ForwardRefExoticComponent<
          Omit<LucideProps, "ref"> & React.RefAttributes<SVGSVGElement>
        >;
        href: undefined;
        items: { name: string; href: string }[];
      }
  )[];
}

const DrawerDashboard: React.FC<DrawerDashboardProps> = ({ sidebar }) => {
  const { closeDrawer, drawerRef, isOpen, setIsOpen } = useDrawer();

  return (
    <Drawer
      open={isOpen}
      onOpenChange={(open) => setIsOpen(open)}
      direction="left"
    >
      <DrawerTrigger asChild>
        <AlignJustify className="block min-[1024px]:hidden cursor-pointer" />
      </DrawerTrigger>
      <DrawerContent
        ref={drawerRef}
        className="w-full sm:w-[16rem] border-none rounded-none overflow-x-hidden overflow-y-auto"
      >
        <div className="container sticky top-0 z-10 bg-background py-4 flex items-center justify-between border-b-[1px] border-[#E9E9EB]">
          <div className="w-full flex justify-between items-center">
            <Link href={ROUTE.HOME} onClick={closeDrawer}>
              <p
                className={`text-[#06042B] font-bold text-lg ${inter.className}`}
              >
                Menu
              </p>
            </Link>

            <div className="flex justify-end items-center gap-3">
              <DrawerClose data-drawer-close>
                <X className="text-[#D8D8DA] cursor-pointer" />
              </DrawerClose>
            </div>
          </div>
        </div>

        <div className="container flex flex-col gap-3 py-6">
          {sidebar.map((menu) => (
            <ItemCollapse
              key={menu.name}
              menu={menu}
              onCloseDrawer={closeDrawer}
            />
          ))}
        </div>
      </DrawerContent>
    </Drawer>
  );
};

export default DrawerDashboard;

interface ItemCollapseProps {
  menu:
    | {
        name: string;
        icon: React.ForwardRefExoticComponent<
          Omit<LucideProps, "ref"> & React.RefAttributes<SVGSVGElement>
        >;
        href: string;
        items: undefined;
      }
    | {
        name: string;
        icon: React.ForwardRefExoticComponent<
          Omit<LucideProps, "ref"> & React.RefAttributes<SVGSVGElement>
        >;
        href: undefined;
        items: { name: string; href: string }[];
      };

  onCloseDrawer: VoidFunction;
}

const ItemCollapse: React.FC<ItemCollapseProps> = ({ menu, onCloseDrawer }) => {
  const [isOpen, setIsOpen] = React.useState(false);

  return (
    <Collapsible open={isOpen} onOpenChange={setIsOpen} className="h-6">
      <div className="flex items-center justify-between">
        {menu.href ? (
          <Link
            href={menu.href}
            onClick={onCloseDrawer}
            className="flex items-center"
          >
            <menu.icon />
            <p
              className={cn(
                "text-base transition duration-200 font-normal pl-2"
              )}
            >
              {menu.name}
            </p>
          </Link>
        ) : (
          <CollapsibleTrigger className="cursor-pointer" asChild>
            <div className="flex items-center">
              <menu.icon />
              <p
                className={cn(
                  "text-base transition duration-200 pl-2",
                  isOpen ? "font-semibold" : "font-normal"
                )}
              >
                {menu.name}
              </p>
              <ChevronDown
                className="relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180"
                aria-hidden="true"
              />
            </div>
          </CollapsibleTrigger>
        )}
      </div>

      <CollapsibleContent className="p-3 flex flex-col gap-2">
        {menu.items?.map((item) => (
          <Link
            href={item.href}
            onClick={onCloseDrawer}
            className="flex items-center"
          >
            <ChevronDown className="invisible" />
            <p className="text-sm text-[#333333] py-1">{item.name}</p>
          </Link>
        ))}
      </CollapsibleContent>
    </Collapsible>
  );
};
