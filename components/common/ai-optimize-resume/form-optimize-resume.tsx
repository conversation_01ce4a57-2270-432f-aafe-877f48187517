"use client";

import { ResponseEvaluate, ResponseUpdateResume } from "@/apis/resume/types";
import { Button } from "@/components/ui/button";
import { Controller, useFormContext } from "react-hook-form";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { cn } from "@/lib/utils";
import React from "react";
import FileUpload from "@/components/common/file-upload";
import ComboboxResume from "@/components/common/combobox-resume";
import { Input } from "@/components/ui/input";
import { useQueryClient } from "@tanstack/react-query";

const RADIO_BUTTON = [
  { value: "select", label: "Select resume from library" },
  { value: "upload", label: "Upload resume" },
];

type RadioButton = "upload" | "select";

export type FormOptimizeResumeType = {
  radio: RadioButton;
  file: File | null;
  selectFile: ResponseUpdateResume | null;
  jobDescription?: string;
  jobUrl?: string;
  jobFile: File | null;
  evaluateData: ResponseEvaluate | null;
};

interface FormOptimizeResumeProps {
  onNextStep: VoidFunction;
}

const FormOptimizeResume: React.FC<FormOptimizeResumeProps> = ({
  onNextStep,
}) => {
  const queryClient = useQueryClient();
  const { control, handleSubmit, watch } =
    useFormContext<FormOptimizeResumeType>();

  const onSubmit = () => {
    onNextStep();
  };

  return (
    <div className="py-6 px-4 sm:px-6 flex flex-col items-center">
      <div className={cn("w-full md:w-[730px]")}>
        <h3 className="text-2xl font-semibold text-center">
          AI Optimize Resume
        </h3>
        <p className="text-base text-[#667085] text-center pt-2">
          Upload your CV to let our AI tailor better job matches for you.
        </p>

        <form onSubmit={handleSubmit(onSubmit)} className="w-full pt-3">
          <Controller
            name="radio"
            control={control}
            render={({ field }) => (
              <RadioGroup
                value={field.value}
                onValueChange={field.onChange}
                className="flex items-center gap-4 pt-4"
              >
                {RADIO_BUTTON.map((item) => (
                  <div className="flex items-center space-x-2" key={item.value}>
                    <RadioGroupItem
                      value={item.value}
                      id={item.value}
                      checked={field.value === item.value}
                    />
                    <Label
                      htmlFor={item.value}
                      className="text-sm md:text-base font-normal"
                    >
                      {item.label}
                    </Label>
                  </div>
                ))}
              </RadioGroup>
            )}
          />

          <div
            className={cn(
              "pt-3",
              watch("radio") === "upload" ? "block" : "hidden"
            )}
          >
            <Controller
              name="file"
              control={control}
              render={({ field: { value, onChange } }) => (
                <FileUpload
                  file={value}
                  onFileSelect={(file) => {
                    queryClient.removeQueries({
                      queryKey: ["ai-resume-score"],
                    });
                    queryClient.removeQueries({
                      queryKey: ["optimize-resume"],
                    });
                    onChange(file);
                  }}
                />
              )}
            />
          </div>

          <div
            className={cn(
              "pt-3",
              watch("radio") === "select" ? "block" : "hidden"
            )}
          >
            <Controller
              name="selectFile"
              control={control}
              render={({ field: { value, onChange } }) => (
                <ComboboxResume
                  resume={value}
                  onChange={(resume) => {
                    queryClient.setQueryData(["optimize-resume"], resume);
                    queryClient.removeQueries({
                      queryKey: ["ai-resume-score"],
                    });
                    onChange(resume);
                  }}
                />
              )}
            />
          </div>

          <div className="pt-8 flex flex-col gap-2">
            <p className="text-base font-medium text-[#06042B]">JD Applied</p>
            {/* <p className="text-xs text-[#49506A] font-normal">
              You can paste text, insert a link, or upload the JD file
            </p> */}
            <Controller
              name="jobDescription"
              control={control}
              render={({ field }) => (
                <textarea
                  {...field}
                  placeholder="Paste the job description here..."
                  className="w-full h-40 p-3 border border-[#E7E7E7] rounded-lg focus:outline-none focus:ring-1 focus:ring-main focus:border-main resize-none"
                />
              )}
            />
            {/* <Controller
              name="jobUrl"
              control={control}
              render={({ field }) => (
                <Input
                  {...field}
                  placeholder="OR Paste job URL (e.g. https://www.career.company.com/job-id)"
                  className="w-full rounded-lg focus:outline-none focus:border-main focus-visible:outline-0"
                  disabled
                />
              )}
            /> */}
            {/* <Controller
              name="jobFile"
              control={control}
              render={({ field: { value, onChange } }) => (
                <FileUpload
                  title="OR Drag and drop job description file here"
                  file={value}
                  onFileSelect={(file) => onChange(file)}
                  disabled
                />
              )}
            /> */}
          </div>

          <div className="pt-5">
            <Button
              type="submit"
              className="w-full px-4 py-5"
              disabled={
                watch("radio") === "upload"
                  ? !watch("file")
                  : !watch("selectFile")
              }
            >
              Optimize
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default FormOptimizeResume;
