import React, { use<PERSON><PERSON>back, useEffect, useRef, useState } from "react";
import { ToggleGroup, ToggleGroupItem } from "../../ui/toggle-group";
import { cn } from "@/lib/utils";
import PreviewFinish from "./preview-finish/preview-finish";
import { useLexicalComposerContext } from "@lexical/react/LexicalComposerContext";
import {
  InitialConfigType,
  LexicalComposer,
} from "@lexical/react/LexicalComposer";
import { $createMarkNode, $isMarkNode, MarkNode } from "@lexical/mark";
import {
  $convertFromMarkdownString,
  $convertToMarkdownString,
  TRANSFORMERS,
} from "@lexical/markdown";
import {
  $createTextNode,
  $getRoot,
  $isElementNode,
  $isTextNode,
  LexicalEditor,
  LexicalNode,
  ParagraphNode,
  TextNode,
} from "lexical";
import { $generateHtmlFromNodes } from "@lexical/html";
import { MarkdownShortcutPlugin } from "@lexical/react/LexicalMarkdownShortcutPlugin";
import { ListPlugin } from "@lexical/react/LexicalListPlugin";
import { HistoryPlugin } from "@lexical/react/LexicalHistoryPlugin";
import { AutoFocusPlugin } from "@lexical/react/LexicalAutoFocusPlugin";
import { HeadingNode } from "@lexical/rich-text";
import { ListNode, ListItemNode } from "@lexical/list";
import { LinkNode } from "@lexical/link";
import { CodeNode } from "@lexical/code";
import { QuoteNode } from "@lexical/rich-text";
import lexicalTheme from "@/thems/lexical-theme";
import Compare from "./preview-finish/compare";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { evaluateCV } from "@/apis/resume/resume";
import { LoadingOverlay } from "@/components/common/loading";
import { MESSAGES_LOADING_EVALUATE_RESUME } from "@/constants/loading-message";
import { useFormContext } from "react-hook-form";
import { FormOptimizeResumeType } from "./form-optimize-resume";
import { useHistoryEditorState } from "@/lib/store/history-editor-store";
import { FeedbackItem, ResponseEvaluate } from "@/apis/resume/types";
import htmlToPdfmake from "html-to-pdfmake";
import pdfMake from "pdfmake/build/pdfmake";
import pdfFonts from "pdfmake/build/vfs_fonts";
import { TDocumentDefinitions } from "pdfmake/interfaces";
import { SuggestionType } from "./preview-finish/overall-score";
import { useToast } from "@/hooks/use-toast";

pdfMake.vfs = pdfFonts as any;

export enum SuggestionStatus {
  SUGGESTION = "SUGGESTION",
  IGNORE = "IGNORE",
  ACCEPT = "ACCEPT",
}

export interface FeedbackState extends FeedbackItem {
  status: SuggestionStatus;
}

// const sampleMarkdown = `
// # [Your Full Name]
// ## Contact Information
// - **Email:** <EMAIL>
// - **Phone:** +1234567890
// - **LinkedIn:** linkedin.com/in/yourprofile
// - **GitHub:** github.com/yourusername
// - **Portfolio:** yourportfolio.com
// - **Location:** [City, Country]
// ## Professional Summary
// [Write a concise 2-4 sentence paragraph about your background, technical expertise, and career goals as a developer]
// ## Technical Skills
// - **Programming Languages:** [e.g., JavaScript, Python, Java, C#, TypeScript]
// - **Frontend:** [e.g., React, Vue.js, Angular, HTML5, CSS3, SASS]
// - **Backend:** [e.g., Node.js, Django, Flask, Spring Boot, Express.js]
// - **Databases:** [e.g., MongoDB, PostgreSQL, MySQL, Firebase]
// - **Cloud Services:** [e.g., AWS, Google Cloud, Azure, Heroku]
// - **DevOps:** [e.g., Docker, Kubernetes, CI/CD, Jenkins]
// - **Testing:** [e.g., Jest, Mocha, Selenium, JUnit]
// - **Other Tools:** [e.g., Git, Jira, Figma, Webpack]
// ## Work Experience
// ### [Company Name] - [Position Title]
// *[Start Date] - [End Date or "Present"]*
// - Developed and maintained [specific features or applications] using [technologies]
// - Improved [performance/security/user experience] by [specific action], resulting in [measurable outcome]
// - Collaborated with cross-functional teams to deliver [project] within [timeframe]
// - [Other significant achievements or responsibilities]
// ### [Company Name] - [Position Title]
// *[Start Date] - [End Date]*
// - Built [specific features or applications] using [technologies]
// - Implemented [specific technical solution] that [solved what problem]
// - Participated in code reviews and mentored junior developers
// - [Other significant achievements or responsibilities]
// ## Projects
// ### [Project Name]
// - **Description:** [Brief description of the project and your role]
// - **Technologies:** [List of technologies used]
// - **Link:** [GitHub repo or live demo]
// - **Key Features:**
//   - [Feature 1]
//   - [Feature 2]
//   - [Feature 3]
// ### [Project Name]
// - **Description:** [Brief description of the project and your role]
// - **Technologies:** [List of technologies used]
// - **Link:** [GitHub repo or live demo]
// - **Key Features:**
//   - [Feature 1]
//   - [Feature 2]
//   - [Feature 3]
// ## Education
// ### [University/College Name]
// - **Degree:** [Bachelor's/Master's/PhD] in [Field of Study]
// - **Duration:** [Start Year] - [End Year]
// - **Relevant Coursework:** [List relevant courses]
// - **GPA:** [Your GPA] (if noteworthy)
// ## Certifications
// - **[Certification Name]** - [Issuing Organization] - [Year]
// - **[Certification Name]** - [Issuing Organization] - [Year]
// ## Additional Experience
// - **Open Source Contributions:** [List significant contributions]
// - **Hackathons:** [List notable hackathons and achievements]
// - **Tech Community:** [Meetups, conferences, or communities you're active in]
// - **Technical Writing:** [Any blog posts, tutorials, or documentation you've written]
// ## Languages
// - **English:** [Proficiency level]
// - **[Other Language]:** [Proficiency level]
// `;

function GetEditorInstance({
  setEditor,
}: {
  setEditor: React.Dispatch<React.SetStateAction<LexicalEditor | null>>;
}) {
  const [editor] = useLexicalComposerContext();

  useEffect(() => {
    setEditor(editor);
  }, [editor, setEditor]);

  return null;
}

const getHtmlContent = (editor: LexicalEditor): Promise<string> => {
  return new Promise((resolve) => {
    editor.update(() => {
      const htmlString = $generateHtmlFromNodes(editor);
      resolve(htmlString);
    });
  });
};

enum OptimizeResumeTabs {
  PREVIEW_FINISH = "PREVIEW_FINISH",
  COMPARE = "COMPARE",
}

interface OptimizeResumeProps {
  filename: string;
  markdown: string;
  evaluateData: ResponseEvaluate;
}

const OptimizeResume: React.FC<OptimizeResumeProps> = ({
  filename,
  markdown,
  evaluateData,
}) => {
  const { toast } = useToast();
  const { getValues, setValue } = useFormContext<FormOptimizeResumeType>();
  const queryClient = useQueryClient();
  const cancelTokenRef = useRef<AbortController | null>(null);
  const [editor, setEditor] = useState<LexicalEditor | null>(null);
  const [tab, setTab] = useState<OptimizeResumeTabs>(
    OptimizeResumeTabs.PREVIEW_FINISH
  );
  const { resetEditorState } = useHistoryEditorState();

  const [initialContent, setInitialContent] = useState<string>("");
  const [modifiedContent, setModifiedContent] = useState<string>("");
  const [isLoading, setIsLoading] = useState<boolean>(false);

  const [suggestions, setSuggestions] = useState<
    Record<SuggestionType, FeedbackState[]>
  >({
    ...Object.fromEntries(
      Object.entries(
        evaluateData.suggestion_optimize.general_cv_quality_scores
      ).map(([key, items]) => [
        key,
        items.map((item: FeedbackItem) => ({
          ...item,
          status: SuggestionStatus.SUGGESTION,
        })),
      ])
    ),
    ...Object.fromEntries(
      Object.entries(evaluateData.suggestion_optimize.job_match_scores).map(
        ([key, items]) => [
          key,
          items.map((item: FeedbackItem) => ({
            ...item,
            status: SuggestionStatus.SUGGESTION,
          })),
        ]
      )
    ),
  } as Record<SuggestionType, FeedbackState[]>);

  const { mutateAsync: mutateEvaluate } = useMutation({
    mutationKey: ["ai-resume-score"],
    mutationFn: (formData: FormData) => {
      cancelTokenRef.current = new AbortController();
      return evaluateCV(formData, cancelTokenRef.current.signal);
    },
    onMutate: () => {
      setIsLoading(true);
    },
    onSuccess: (data) => {
      const evaluateData = getValues("evaluateData") as ResponseEvaluate;
      setValue("evaluateData", {
        ...evaluateData,
        detailedEvaluation: {
          ...evaluateData.detailedEvaluation,
          combined_total_score: data.detailedEvaluation.combined_total_score,
        },
      });
      queryClient.invalidateQueries({ queryKey: ["ai-resume-score"] });
      setIsLoading(false);
    },
    onError: () => {
      setIsLoading(false);
    },
  });

  const onRescore = async () => {
    if (!editor) return;
    const markdown = editor.getEditorState().read(() => {
      return $convertToMarkdownString(TRANSFORMERS);
    });
    const { jobDescription } = getValues();
    const formData = new FormData();
    formData.append("markdown_content", markdown);
    if (jobDescription) formData.append("jobDescription", jobDescription);
    await mutateEvaluate(formData);
  };

  const updateContent = useCallback(() => {
    if (!editor) return;

    getHtmlContent(editor).then((htmlContent) => {
      setModifiedContent(htmlContent);
    });
  }, [editor]);

  const onDownload = () => {
    // Always get content from RichText mode
    const editorContent = document.getElementById("download-resume");
    if (!editorContent) return;

    // Convert HTML to PDFMake format
    const pdfContent = htmlToPdfmake(editorContent.innerHTML);

    // Configure PDF document
    const docDefinition: TDocumentDefinitions = {
      content: pdfContent,
      pageSize: "LETTER",
      pageMargins: [20, 20, 20, 20],
      defaultStyle: {
        fontSize: 12,
        lineHeight: 1.5,
      },
    };

    // Generate and download PDF
    pdfMake.createPdf(docDefinition).download(filename);
  };

  const onHighlight = (words: string[]) => {
    if (!editor) return;

    // Biến để theo dõi node highlight cuối cùng
    let lastHighlightedNode: LexicalNode | null = null;

    editor.update(
      () => {
        const root = $getRoot();

        // Chỉ xóa highlight cũ, giữ lại các từ đã thay thế
        const removeExistingHighlights = (node: LexicalNode) => {
          if ($isMarkNode(node) && node.getIDs().includes("highlight")) {
            // Lấy nội dung của MarkNode
            const text = node.getTextContent();
            // Tạo TextNode mới không có highlight
            const newTextNode = $createTextNode(text);
            // Thay thế MarkNode bằng TextNode mới
            node.replace(newTextNode);
          } else if ($isElementNode(node)) {
            // Duyệt qua tất cả các node con
            node.getChildren().forEach(removeExistingHighlights);
          }
        };

        // Bắt đầu xóa highlight từ root
        root.getChildren().forEach(removeExistingHighlights);

        // Sau khi đã xóa tất cả highlight, tiếp tục với việc highlight từ mới
        const textNodes: TextNode[] = [];

        // Hàm đệ quy để duyệt qua tất cả các node, bao gồm cả cấu trúc lồng nhau
        const collectTextNodes = (node: LexicalNode) => {
          if ($isTextNode(node)) {
            // Kiểm tra nếu node này đã được thay thế (nằm trong MarkNode với ID 'replaced')
            const parent = node.getParent();
            if ($isMarkNode(parent) && parent.getIDs().includes("replaced")) {
              // Bỏ qua node này vì nó đã được thay thế
              return;
            }
            textNodes.push(node);
          } else if ($isElementNode(node)) {
            // Duyệt qua tất cả các node con
            node.getChildren().forEach(collectTextNodes);
          }
        };

        // Bắt đầu duyệt từ root
        root.getChildren().forEach(collectTextNodes);

        console.log(`Found ${textNodes.length} text nodes`);

        // Process each text node
        textNodes.forEach((textNode, index) => {
          let text = textNode.getTextContent();
          // Thêm thông tin về parent node để dễ debug
          const parentNode = textNode.getParent();
          const parentType = parentNode ? parentNode.getType() : "unknown";

          console.log(`Node #${index + 1}: "${text}" (parent: ${parentType})`);

          let newNodes: LexicalNode[] = [];
          let lastIndex = 0;

          console.log({ text });

          words.forEach((word) => {
            const regex = new RegExp(`\\b${word}\\b`, "gi");
            let match;

            while ((match = regex.exec(text)) !== null) {
              const start = match.index;
              const end = start + word.length;

              // Add text before the match
              if (start > lastIndex) {
                newNodes.push($createTextNode(text.slice(lastIndex, start)));
              }

              // Create text node with the matched word
              const highlightedNode = $createTextNode(text.slice(start, end));

              // Apply styles for visual highlighting (red background)
              highlightedNode.setStyle(
                "background-color: #FF0000; color: white;"
              );

              // Create a MarkNode with the "highlight" ID and wrap the text node
              const markNode = $createMarkNode(["highlight"]);
              markNode.append(highlightedNode);

              // Add the mark node to our array
              newNodes.push(markNode);

              // Lưu lại node highlight cuối cùng
              lastHighlightedNode = markNode;

              lastIndex = end;
            }
          });

          // Add remaining text
          if (lastIndex < text.length) {
            newNodes.push($createTextNode(text.slice(lastIndex)));
          }

          // Replace the original node with new nodes
          if (newNodes.length > 0) {
            textNode.replace(newNodes[0]);
            for (let i = 1; i < newNodes.length; i++) {
              newNodes[i - 1].insertAfter(newNodes[i]);
            }
          }
        });
      },
      {
        onUpdate: () => {
          // Sau khi update hoàn tất, scroll đến node highlight cuối cùng
          if (lastHighlightedNode) {
            setTimeout(() => {
              const domNode = editor.getElementByKey(
                lastHighlightedNode!.getKey()
              );
              if (domNode) {
                // Scroll đến phần tử với hiệu ứng mượt mà
                domNode.scrollIntoView({ behavior: "smooth", block: "center" });
              }
            }, 100); // Đợi một chút để DOM được cập nhật hoàn toàn
          }
        },
      }
    );
  };

  const onApply = (
    searchTexts: string[],
    replaceTexts: string[],
    callback: (successfulReplacements: string[]) => void
  ) => {
    if (!editor || searchTexts.length !== replaceTexts.length) return;

    // Biến để theo dõi node highlight cuối cùng
    let lastHighlightedNode: LexicalNode | null = null;
    // Mảng chứa các searchText đã được thay thế thành công
    const successfulReplacements: string[] = [];

    editor.update(
      () => {
        const root = $getRoot();

        // Xóa tất cả các highlight hiện tại trước tiên
        const removeExistingHighlights = (node: LexicalNode) => {
          if ($isMarkNode(node) && node.getIDs().includes("highlight")) {
            // Lấy nội dung của MarkNode
            const text = node.getTextContent();
            // Tạo TextNode mới không có highlight
            const newTextNode = $createTextNode(text);
            // Thay thế MarkNode bằng TextNode mới
            node.replace(newTextNode);
          } else if ($isElementNode(node)) {
            // Duyệt qua tất cả các node con
            node.getChildren().forEach(removeExistingHighlights);
          }
        };

        // Bắt đầu xóa highlight từ root
        root.getChildren().forEach(removeExistingHighlights);

        // Thu thập tất cả các TextNode để tìm kiếm và thay thế
        const textNodes: TextNode[] = [];

        // Hàm đệ quy để duyệt qua tất cả các node, bao gồm cả cấu trúc lồng nhau
        const collectTextNodes = (node: LexicalNode) => {
          if ($isTextNode(node)) {
            textNodes.push(node);
          } else if ($isElementNode(node)) {
            // Duyệt qua tất cả các node con
            node.getChildren().forEach(collectTextNodes);
          }
        };

        // Bắt đầu duyệt từ root
        root.getChildren().forEach(collectTextNodes);

        console.log(`Found ${textNodes.length} text nodes for replacement`);

        // Process each text node
        textNodes.forEach((textNode) => {
          let text = textNode.getTextContent();
          let hasChanges = false;
          let newNodes: LexicalNode[] = [];
          let lastIndex = 0;

          // Kiểm tra từng cặp search/replace
          for (let i = 0; i < searchTexts.length; i++) {
            const searchText = searchTexts[i];
            const replaceText = replaceTexts[i];
            const regex = new RegExp(`\\b${searchText}\\b`, "gi");

            // Nếu text chứa từ cần tìm
            if (regex.test(text)) {
              hasChanges = true;
              // Đánh dấu searchText này đã được thay thế thành công
              if (!successfulReplacements.includes(searchText)) {
                successfulReplacements.push(searchText);
              }

              // Reset regex để sử dụng lại từ đầu
              regex.lastIndex = 0;

              // Biến tạm để xây dựng text mới
              let tempText = text;
              let tempLastIndex = 0;
              let tempNewNodes: LexicalNode[] = [];
              let match;

              while ((match = regex.exec(tempText)) !== null) {
                const start = match.index;
                const end = start + searchText.length;

                // Thêm text trước match
                if (start > tempLastIndex) {
                  tempNewNodes.push(
                    $createTextNode(tempText.slice(tempLastIndex, start))
                  );
                }

                // Tạo node cho text đã thay đổi
                const replacedNode = $createTextNode(replaceText);

                // Áp dụng style với màu xanh
                replacedNode.setStyle(
                  "color: #3B82F6; background-color: transparent; font-weight: bold;"
                );

                // Tạo MarkNode với ID "replaced" và bọc TextNode
                const markNode = $createMarkNode(["replaced"]).setStyle(
                  "background-color: red;"
                );
                markNode.append(replacedNode);

                // Thêm vào mảng node
                tempNewNodes.push(markNode);

                // Lưu lại node được highlight cuối cùng
                lastHighlightedNode = markNode;

                tempLastIndex = end;
              }

              // Thêm text còn lại
              if (tempLastIndex < tempText.length) {
                tempNewNodes.push(
                  $createTextNode(tempText.slice(tempLastIndex))
                );
              }

              // Cập nhật text và nodes
              newNodes = tempNewNodes;
              break; // Chỉ xử lý 1 lần thay thế
            }
          }

          // Nếu có thay đổi, thay thế node hiện tại
          if (hasChanges && newNodes.length > 0) {
            textNode.replace(newNodes[0]);
            for (let i = 1; i < newNodes.length; i++) {
              newNodes[i - 1].insertAfter(newNodes[i]);
            }
          }
        });
      },
      {
        onUpdate: () => {
          // Sau khi update hoàn tất, scroll đến node đã thay đổi cuối cùng
          if (lastHighlightedNode) {
            // Gọi callback với danh sách các searchText đã được thay thế thành công
            callback(successfulReplacements);
            setTimeout(() => {
              const domNode = editor.getElementByKey(
                lastHighlightedNode!.getKey()
              );
              if (domNode) {
                // Scroll đến phần tử với hiệu ứng mượt mà
                domNode.scrollIntoView({ behavior: "smooth", block: "center" });
              }
            }, 100);
          } else {
            // Nếu không có node nào được highlight, vẫn gọi callback với danh sách rỗng
            callback(successfulReplacements);

            // Tìm những từ có trong searchTexts nhưng không có trong successfulReplacements
            const notFoundWords = searchTexts.filter(
              (word) => !successfulReplacements.includes(word)
            );

            if (notFoundWords.length > 0) {
              toast({
                title: "Not Found",
                description: (
                  <div>
                    <p>Could not find the following words:</p>
                    {notFoundWords.map((word, index) => (
                      <p key={index}>• {word}</p>
                    ))}
                  </div>
                ),
                variant: "destructive",
              });
            }
          }
        },
      }
    );
  };

  const onCancelUpload = () => {
    if (cancelTokenRef.current) {
      cancelTokenRef.current.abort();
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (editor) {
      getHtmlContent(editor).then((htmlContent) => {
        setInitialContent(htmlContent);
        setModifiedContent(htmlContent);
      });

      // Đăng ký theo dõi sự thay đổi
      const removeUpdateListener = editor.registerUpdateListener(
        ({ editorState }) => {
          editorState.read(() => {
            updateContent();
          });
        }
      );

      // Hủy đăng ký khi component unmount
      return () => {
        removeUpdateListener();
      };
    }
  }, [editor, updateContent]);

  const renderTab = () => {
    if (tab === OptimizeResumeTabs.PREVIEW_FINISH)
      return (
        <PreviewFinish
          evaluateData={evaluateData}
          onRescore={onRescore}
          onApply={onApply}
          onHighlight={onHighlight}
          suggestions={suggestions}
          setSuggestions={setSuggestions}
        />
      );
    if (tab === OptimizeResumeTabs.COMPARE)
      return (
        <Compare
          oldHtml={initialContent}
          newHtml={modifiedContent}
          onDownload={onDownload}
        />
      );
  };

  const editorConfig: InitialConfigType = {
    namespace: "React.js Demo",
    nodes: [
      ParagraphNode,
      TextNode,
      HeadingNode,
      ListNode,
      ListItemNode,
      LinkNode,
      CodeNode,
      QuoteNode,
      MarkNode,
    ],
    onError(error: Error) {
      throw error;
    },
    theme: lexicalTheme,
  };

  // Reset editor state khi component unmount
  useEffect(() => {
    return () => {
      resetEditorState();
    };
  }, [resetEditorState]);

  useEffect(() => {
    setSuggestions({
      ...Object.fromEntries(
        Object.entries(
          evaluateData.suggestion_optimize.general_cv_quality_scores
        ).map(([key, items]) => [
          key,
          items.map((item: FeedbackItem) => ({
            ...item,
            status: SuggestionStatus.SUGGESTION,
          })),
        ])
      ),
      ...Object.fromEntries(
        Object.entries(evaluateData.suggestion_optimize.job_match_scores).map(
          ([key, items]) => [
            key,
            items.map((item: FeedbackItem) => ({
              ...item,
              status: SuggestionStatus.SUGGESTION,
            })),
          ]
        )
      ),
    } as Record<SuggestionType, FeedbackState[]>);
  }, [evaluateData]);

  return (
    <div className="px-6 py-5 pt-3">
      {isLoading && (
        <LoadingOverlay
          messages={MESSAGES_LOADING_EVALUATE_RESUME}
          onCancel={onCancelUpload}
        />
      )}

      <ToggleGroup
        type="single"
        value={tab}
        onValueChange={(value: OptimizeResumeTabs) => setTab(value)}
        className="gap-0 w-fit p-0.5 border border-[#E0E2E7] rounded-lg bg-background"
      >
        <ToggleGroupItem
          value={OptimizeResumeTabs.PREVIEW_FINISH}
          aria-label="Optimize Resume"
          className={cn(
            "text-[#363A58] text-sm rounded-lg hover:text-[#363A58] h-auto py-1.5 px-4",
            tab === OptimizeResumeTabs.PREVIEW_FINISH
              ? "!bg-[#7E77F8] !text-background"
              : ""
          )}
        >
          Optimize Resume
        </ToggleGroupItem>
        <ToggleGroupItem
          value={OptimizeResumeTabs.COMPARE}
          aria-label="Compare changes"
          className={cn(
            "text-[#363A58] text-sm rounded-lg hover:text-[#363A58] h-auto py-1.5 px-4",
            tab === OptimizeResumeTabs.COMPARE
              ? "!bg-[#7E77F8] !text-background"
              : ""
          )}
        >
          Compare changes
        </ToggleGroupItem>
      </ToggleGroup>

      <div className="pt-5 rounded-lg">
        <LexicalComposer
          initialConfig={{
            ...editorConfig,
            editorState: () =>
              $convertFromMarkdownString(markdown, TRANSFORMERS),
            onError(error: Error) {
              throw error;
            },
          }}
        >
          {renderTab()}
          <MarkdownShortcutPlugin transformers={TRANSFORMERS} />
          <ListPlugin />
          <HistoryPlugin />
          <AutoFocusPlugin />
          {editor === null && <GetEditorInstance setEditor={setEditor} />}
        </LexicalComposer>
      </div>
    </div>
  );
};

export default OptimizeResume;
