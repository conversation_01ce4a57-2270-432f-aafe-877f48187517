"use client";

import * as React from "react";
import { useState } from "react";
import { useMutation } from "@tanstack/react-query";

import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";

import SuggestionIcon from "@/public/images/ai-optimize-resume/suggestion.svg";
import IgnoreIcon from "@/public/images/ai-optimize-resume/suggestion_ignore.svg";
import AcceptIcon from "@/public/images/ai-optimize-resume/suggestion_accept.svg";
import GenerateIcon from "@/public/images/ai-optimize-resume/suggestion_generate.svg";
import Image from "next/image";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { ArrowRight, Trash2 } from "lucide-react";
import { cn } from "@/lib/utils";
import { FeedbackState, SuggestionStatus } from "../optimize-resume";
import { rewriteSuggestion } from "@/apis/resume/resume";
import { ERewriteSuggestion } from "@/apis/resume/types";

interface SuggestionProps {
  feedback: FeedbackState;
  onHighlight: (words: string[]) => void;
  onApply: (originalText: string[], suggestionText: string[]) => void;
  onIgnore: (feedback: string) => void;
  onDelete: (feedback: string) => void;
  onUpdateSuggestion?: (feedback: string, newSuggestedText: string) => void;
}

const Suggestion: React.FC<SuggestionProps> = ({
  feedback,
  onHighlight,
  onApply,
  onIgnore,
  onDelete,
  onUpdateSuggestion,
}) => {
  const [isOpen, setIsOpen] = React.useState(false);
  const [selectedStyle, setSelectedStyle] = useState<ERewriteSuggestion | null>(
    null
  );

  const { mutate: mutateRewrite, isPending: isRewriting } = useMutation({
    mutationFn: rewriteSuggestion,
    onSuccess: (response) => {
      if (onUpdateSuggestion && response.rewritten_text) {
        onUpdateSuggestion(feedback.feedback, response.rewritten_text);
      } else {
        console.error(
          "❌ Missing response.rewritten_text or onUpdateSuggestion callback"
        );
      }
      setSelectedStyle(null); // Reset selection after success
    },
    onError: (error) => {
      console.error("Rewrite failed:", error);
    },
  });

  const handleStyleSelect = (style: ERewriteSuggestion) => {
    setSelectedStyle(selectedStyle === style ? null : style);
  };

  const handleAIGenerate = () => {
    if (!selectedStyle || !feedback.suggested_text || isRewriting) return;

    mutateRewrite({
      text: feedback.suggested_text,
      style: selectedStyle,
    });
  };

  const canRewrite = Boolean(
    feedback.suggested_text &&
      feedback.original_text &&
      feedback.is_can_auto_apply
  );

  return (
    <Collapsible
      open={isOpen}
      onOpenChange={setIsOpen}
      className={cn(
        "w-[380px] border border-[#E0E2E7] rounded-lg h-fit overflow-hidden",
        isOpen ? "pt-4" : "py-4",
        feedback.status === SuggestionStatus.IGNORE ||
          feedback.status === SuggestionStatus.ACCEPT ||
          !feedback.is_can_auto_apply
          ? "py-4"
          : ""
      )}
    >
      <CollapsibleTrigger asChild>
        <div className="cursor-pointer flex items-start gap-4 px-3">
          <Image
            src={
              feedback.status === SuggestionStatus.ACCEPT
                ? AcceptIcon
                : feedback.status === SuggestionStatus.IGNORE
                ? IgnoreIcon
                : SuggestionIcon
            }
            alt="Suggestion"
            width={24}
            height={24}
          />
          <p className="text-sm text-[#363A58]">{feedback.feedback}</p>
        </div>
      </CollapsibleTrigger>

      <CollapsibleContent
        className={cn(
          "pt-2",
          feedback.status === SuggestionStatus.IGNORE ||
            feedback.status === SuggestionStatus.ACCEPT
            ? "hidden"
            : "block"
        )}
      >
        <div className="flex flex-col gap-4 justify-end">
          <div className="flex justify-end px-3">
            <Button
              variant="danger-outline"
              className="text-xs px-2 py-1 rounded-md w-fit h-fit"
              onClick={() => onIgnore(feedback.feedback)}
            >
              Ignore
            </Button>
          </div>

          {canRewrite && feedback.status === SuggestionStatus.SUGGESTION ? (
            <Separator />
          ) : null}
        </div>

        {canRewrite && feedback.status === SuggestionStatus.SUGGESTION && (
          <>
            <SuggestionItem
              feedback={feedback}
              onApply={onApply}
              onDelete={onDelete}
              onHighlight={onHighlight}
              isLoading={isRewriting}
            />
            <SuggestionButtonBottom
              selectedStyle={selectedStyle}
              onStyleSelect={handleStyleSelect}
              onAIGenerate={handleAIGenerate}
              isLoading={isRewriting}
            />
          </>
        )}
      </CollapsibleContent>
    </Collapsible>
  );
};

export default Suggestion;

interface SuggestionItemProps {
  feedback: FeedbackState;
  onApply: (originalText: string[], suggestionText: string[]) => void;
  onDelete: (feedback: string) => void;
  onHighlight: (words: string[]) => void;
  isLoading: boolean;
}

const SuggestionItem: React.FC<SuggestionItemProps> = ({
  feedback,
  onApply,
  onDelete,
  onHighlight,
  isLoading,
}) => {
  return (
    <div className="flex flex-col gap-4 justify-end pt-4">
      <div className="flex flex-col gap-2 px-3">
        <div
          className="flex items-start gap-4 cursor-pointer"
          onClick={() => onHighlight([feedback.original_text || ""])}
        >
          <Image alt="" src={GenerateIcon} />
          <p className="text-sm text-[#06042B] ruby">
            <span>{feedback.original_text}</span> <ArrowRight size={16} />{" "}
            <span>{feedback.suggested_text}</span>
          </p>
        </div>

        <div className="flex items-center justify-end gap-1">
          <Button
            className="text-xs px-3 py-1 rounded-md w-fit h-fit"
            disabled={isLoading}
            onClick={() =>
              onApply(
                [feedback.original_text || ""],
                [feedback.suggested_text || ""]
              )
            }
          >
            Apply
          </Button>
          <Button
            variant="secondary"
            className="p-1.5 rounded-md w-fit h-fit"
            disabled={isLoading}
            onClick={() => onDelete(feedback.feedback)}
          >
            <Trash2 size={12} />
          </Button>
        </div>
      </div>
      <Separator />
    </div>
  );
};

interface SuggestionButtonBottomProps {
  selectedStyle: ERewriteSuggestion | null;
  onStyleSelect: (style: ERewriteSuggestion) => void;
  onAIGenerate: () => void;
  isLoading: boolean;
}

const SuggestionButtonBottom: React.FC<SuggestionButtonBottomProps> = ({
  selectedStyle,
  onStyleSelect,
  onAIGenerate,
  isLoading,
}) => {
  const buttonConfig = [
    { style: ERewriteSuggestion.STANDARD, label: "Standard" },
    { style: ERewriteSuggestion.SHORTEN, label: "Shorten" },
    { style: ERewriteSuggestion.FORMAL, label: "Formal" },
    { style: ERewriteSuggestion.LONGER, label: "Longer" },
  ];

  return (
    <div className="flex gap-1 items-center p-3 justify-between bg-[#F3F2FF]">
      {buttonConfig.map(({ style, label }) => (
        <Button
          key={style}
          variant="secondary"
          className={cn(
            "text-xs px-2 py-1 rounded-lg w-fit h-fit",
            selectedStyle === style && "bg-[#7E77F8] text-white"
          )}
          onClick={() => onStyleSelect(style)}
          disabled={isLoading}
        >
          {label}
        </Button>
      ))}

      <Button
        className="text-xs px-2 py-1 rounded-lg w-fit h-fit"
        onClick={onAIGenerate}
        disabled={!selectedStyle}
      >
        {isLoading ? "Generating..." : "AI Generate"}
      </Button>
    </div>
  );
};
