import { FeedbackItem, ResponseEvaluate } from "@/apis/resume/types";
import { But<PERSON> } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import React, { useEffect, useState } from "react";
import { Info } from "lucide-react";
import { Progress } from "@/components/ui/progress";
import Suggestion from "./suggestion";
import { getColorByScore } from "@/utils/colors";
import { FeedbackState, SuggestionStatus } from "../optimize-resume";
import { useFormContext } from "react-hook-form";
import { FormOptimizeResumeType } from "../form-optimize-resume";
import {
  Toolt<PERSON>,
  TooltipArrow,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";

export type SuggestionType =
  | "impact"
  | "brevity"
  | "style"
  | "spelling_grammar"
  | "contact_information"
  | "skills_match"
  | "relevant_keywords"
  | "experience_alignment";

interface OverallScoreProps {
  evaluateData: ResponseEvaluate;
  onRescore: VoidFunction;
  onApply: (
    originalTexts: string[],
    suggestionTexts: string[],
    callback: (successfulReplacements: string[]) => void
  ) => void;
  onHighlight: (words: string[]) => void;
  suggestions: Record<SuggestionType, FeedbackState[]>;
  setSuggestions: (
    suggestions: Record<SuggestionType, FeedbackState[]>
  ) => void;
}

const OverallScore: React.FC<OverallScoreProps> = ({
  evaluateData,
  onRescore,
  onApply,
  onHighlight,
  suggestions,
  setSuggestions,
}) => {
  const { getValues } = useFormContext<FormOptimizeResumeType>();
  const [selectedType, setSelectedType] = useState<SuggestionType | null>(null);
  const [open, setOpen] = useState<boolean>(false);

  const onApplyAllSuggestions = () => {
    const originalTexts = Object.keys(suggestions).flatMap((key) =>
      suggestions[key as SuggestionType]
        .map((item) => item.original_text)
        .filter((text): text is string => text !== undefined)
    );

    const suggestionTexts = Object.keys(suggestions).flatMap((key) =>
      suggestions[key as SuggestionType]
        .map((item) => item.suggested_text)
        .filter((text): text is string => text !== undefined)
    );

    // Tạo một map để dễ dàng truy cập suggestion theo original_text
    const suggestionMap = new Map<
      string,
      { type: SuggestionType; index: number }
    >();

    Object.keys(suggestions).forEach((key) => {
      suggestions[key as SuggestionType].forEach((item, index) => {
        if (item.original_text) {
          suggestionMap.set(item.original_text, {
            type: key as SuggestionType,
            index,
          });
        }
      });
    });

    onApply(
      originalTexts,
      suggestionTexts,
      (successfulReplacements: string[]) => {
        // Chỉ cập nhật trạng thái cho những đề xuất đã được thay thế thành công
        const updatedSuggestions = { ...suggestions };

        // Duyệt qua danh sách các đoạn văn bản đã được thay thế thành công
        successfulReplacements.forEach((originalText: string) => {
          const suggestionInfo = suggestionMap.get(originalText);
          if (suggestionInfo) {
            const { type, index } = suggestionInfo;
            // Cập nhật trạng thái thành ACCEPT
            updatedSuggestions[type][index] = {
              ...updatedSuggestions[type][index],
              status: SuggestionStatus.ACCEPT,
            };
          }
        });

        setSuggestions(updatedSuggestions);
      }
    );
  };

  const onApplySuggestion = (
    originalText: string[],
    suggestionText: string[]
  ) => {
    if (!selectedType || !originalText[0] || !suggestionText[0]) return;

    onApply(
      originalText,
      suggestionText,
      (successfulReplacements: string[]) => {
        if (successfulReplacements.includes(originalText[0])) {
          setSuggestions({
            ...suggestions,
            [selectedType]: suggestions[selectedType].map((item) =>
              item.original_text === originalText[0] &&
              item.suggested_text === suggestionText[0]
                ? { ...item, status: SuggestionStatus.ACCEPT }
                : item
            ),
          });
        }
      }
    );
  };

  const onIgnore = (feedback: string) => {
    if (!selectedType) return;

    setSuggestions({
      ...suggestions,
      [selectedType]: suggestions[selectedType].map((item) =>
        item.feedback === feedback
          ? { ...item, status: SuggestionStatus.IGNORE }
          : item
      ),
    });
  };

  const onDelete = (feedback: string) => {
    if (!selectedType) return;

    const updatedSuggestions = {
      ...suggestions,
      [selectedType]: suggestions[selectedType].filter(
        (item) => item.feedback !== feedback
      ),
    };

    setSuggestions(updatedSuggestions);
  };

  const onUpdateSuggestion = (feedback: string, newSuggestedText: string) => {
    if (!selectedType) return;

    const updatedSuggestions = {
      ...suggestions,
      [selectedType]: suggestions[selectedType].map((item) =>
        item.feedback.trim() === feedback.trim()
          ? { ...item, suggested_text: newSuggestedText }
          : item
      ),
    };

    setSuggestions(updatedSuggestions);
  };

  //
  //
  //

  useEffect(() => {
    setSelectedType(null);
  }, [evaluateData]);

  return (
    <>
      <div className="flex gap-4">
        {selectedType && suggestions[selectedType]?.length ? (
          <div className="flex flex-col gap-3">
            {suggestions[selectedType].map((item, index) => (
              <Suggestion
                key={`${selectedType}-${index}-${item.feedback}`}
                feedback={item}
                onHighlight={onHighlight}
                onApply={onApplySuggestion}
                onIgnore={onIgnore}
                onDelete={onDelete}
                onUpdateSuggestion={onUpdateSuggestion}
              />
            ))}
          </div>
        ) : null}
        <div className="flex flex-col gap-1 items-center border-l border-[#F3F3F3]">
          <div className="flex flex-col justify-center gap-1 border-b border-[#F3F3F3] px-6 py-6">
            <p className="font-bold text-base whitespace-nowrap">
              Overall Score
            </p>

            <p
              className={cn(
                "text-[40px] leading-tight font-bold",
                getColorByScore(
                  evaluateData.detailedEvaluation.combined_total_score
                )
              )}
            >
              {evaluateData.detailedEvaluation.combined_total_score}
            </p>

            <div className="flex items-center gap-1.5">
              <Button
                variant="secondary"
                onClick={onRescore}
                className="h-10 w-32 rounded-lg"
              >
                Rescore
              </Button>

              <TooltipProvider delayDuration={0}>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Info className="text-background fill-[#667085] cursor-pointer" />
                  </TooltipTrigger>
                  <TooltipContent
                    align="end"
                    side="bottom"
                    className="bg-gray-700 border-none max-w-[200px]"
                  >
                    <TooltipArrow className="border-none fill-gray-700" />
                    <p className="text-sm text-gray-50">
                      To re-evaluate resume again after changes are made
                    </p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>
          </div>

          <div className="px-4 py-6 flex flex-col gap-3 w-full h-full">
            <p className="text-base font-bold text-[#06042B]">
              Checkout below Feedback
            </p>

            <ItemProgress
              title="Impact"
              score={
                evaluateData.detailedEvaluation.general_assessment.scores[
                  "impact"
                ].score
              }
              count={suggestions["impact"]?.length}
              onClick={() =>
                setSelectedType((preValue) =>
                  preValue !== "impact" ? "impact" : null
                )
              }
              selected={selectedType === "impact"}
            />
            <ItemProgress
              title="Brevity"
              count={suggestions["brevity"]?.length}
              score={
                evaluateData.detailedEvaluation.general_assessment.scores[
                  "brevity"
                ].score
              }
              onClick={() =>
                setSelectedType((preValue) =>
                  preValue !== "brevity" ? "brevity" : null
                )
              }
              selected={selectedType === "brevity"}
            />
            <ItemProgress
              title="Style"
              count={suggestions["style"]?.length}
              score={
                evaluateData.detailedEvaluation.general_assessment.scores[
                  "style"
                ].score
              }
              onClick={() =>
                setSelectedType((preValue) =>
                  preValue !== "style" ? "style" : null
                )
              }
              selected={selectedType === "style"}
            />
            <ItemProgress
              title="Grammar"
              count={suggestions["spelling_grammar"]?.length}
              score={
                evaluateData.detailedEvaluation.general_assessment.scores[
                  "spelling_grammar"
                ].score
              }
              onClick={() =>
                setSelectedType((preValue) =>
                  preValue !== "spelling_grammar" ? "spelling_grammar" : null
                )
              }
              selected={selectedType === "spelling_grammar"}
            />
            <ItemProgress
              title="Contact information"
              count={0}
              score={
                evaluateData.detailedEvaluation.general_assessment.scores[
                  "contact_information"
                ].score
              }
              onClick={() =>
                setSelectedType((preValue) =>
                  preValue !== "contact_information"
                    ? "contact_information"
                    : null
                )
              }
              selected={selectedType === "contact_information"}
            />
            <ItemProgress
              title="Skills match"
              count={suggestions["skills_match"]?.length}
              score={
                evaluateData.detailedEvaluation.job_match_assessment?.scores?.[
                  "skills_match"
                ]?.score
              }
              onClick={() =>
                setSelectedType((preValue) =>
                  preValue !== "skills_match" ? "skills_match" : null
                )
              }
              selected={selectedType === "skills_match"}
              className={getValues("jobDescription") ? "" : "hidden"}
            />
            <ItemProgress
              title="Relevant keywords"
              count={suggestions["relevant_keywords"]?.length}
              score={
                evaluateData.detailedEvaluation.job_match_assessment?.scores?.[
                  "relevant_keywords"
                ]?.score
              }
              onClick={() =>
                setSelectedType((preValue) =>
                  preValue !== "relevant_keywords" ? "relevant_keywords" : null
                )
              }
              selected={selectedType === "relevant_keywords"}
              className={getValues("jobDescription") ? "" : "hidden"}
            />
            <ItemProgress
              title="Experience match"
              count={suggestions["experience_alignment"]?.length}
              score={
                evaluateData.detailedEvaluation.job_match_assessment?.scores?.[
                  "experience_alignment"
                ]?.score
              }
              onClick={() =>
                setSelectedType((preValue) =>
                  preValue !== "experience_alignment"
                    ? "experience_alignment"
                    : null
                )
              }
              selected={selectedType === "experience_alignment"}
              className={getValues("jobDescription") ? "" : "hidden"}
            />
            <div className="pt-3 mx-auto">
              <Button
                className="px-2 py-2.5 rounded-lg text-sm font-medium"
                onClick={() => setOpen(true)}
              >
                Apply All Suggestions
              </Button>
            </div>
          </div>
        </div>
      </div>

      <DialogApplyAllFeedback
        open={open}
        setOpen={setOpen}
        onOk={() => {
          setOpen(false);
          onApplyAllSuggestions();
        }}
      />
    </>
  );
};

export default OverallScore;

const ItemProgress: React.FC<{
  title: string;
  count: number;
  score?: number;
  onClick: VoidFunction;
  selected?: boolean;
  className?: string;
}> = ({ title, count, score, onClick, selected, className }) => {
  return (
    <div
      className={cn(
        "p-3 border border-[#F3F3F3] rounded-lg flex flex-col gap-2 cursor-pointer transition-all hover:bg-[#F3F2FF]",
        className,
        selected ? "bg-[#F3F2FF] border border-[#7E77F8]" : ""
      )}
      onClick={onClick}
    >
      <p className="text-sm text-[#667085]">
        {title} <span className={!count ? "hidden" : ""}>({count})</span>
      </p>
      <Progress value={score} className="h-2" />
    </div>
  );
};

const DialogApplyAllFeedback = ({
  open,
  setOpen,
  onOk,
}: {
  open: boolean;
  setOpen: (open: boolean) => void;
  onOk: VoidFunction;
}) => {
  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="font-medium">
            Do you want AI to apply all suggestions to your resume?
          </DialogTitle>
        </DialogHeader>
        <DialogFooter className="justify-end gap-2">
          <DialogClose asChild>
            <Button
              variant="secondary"
              className="py-0 !m-0 focus-visible:ring-0 focus-visible:ring-offset-0 focus-visible:ring-[#D9D9D9]"
            >
              Cancel
            </Button>
          </DialogClose>
          <Button className="py-0 !m-0" onClick={onOk}>
            Yes
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
