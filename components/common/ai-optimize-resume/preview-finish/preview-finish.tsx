"use client";

import React from "react";

import Toolbar from "./tool-bar/tool-bar";
import { RichTextPlugin } from "@lexical/react/LexicalRichTextPlugin";
import { ContentEditable } from "@lexical/react/LexicalContentEditable";
import { LexicalErrorBoundary } from "@lexical/react/LexicalErrorBoundary";
import { useLexicalComposerContext } from "@lexical/react/LexicalComposerContext";
import OverallScore, { SuggestionType } from "./overall-score";
import { FeedbackState } from "../optimize-resume";

interface PreviewFinishProps {
  evaluateData: any;
  onRescore: VoidFunction;
  onApply: (
    searchTexts: string[],
    replaceTexts: string[],
    callback: (successfulReplacements: string[]) => void
  ) => void;
  onHighlight: (words: string[]) => void;
  suggestions: Record<SuggestionType, FeedbackState[]>;
  setSuggestions: (
    suggestions: Record<SuggestionType, FeedbackState[]>
  ) => void;
}

const PreviewFinish: React.FC<PreviewFinishProps> = ({
  evaluateData,
  onRescore,
  onApply,
  onHighlight,
  suggestions,
  setSuggestions,
}) => {
  const [editor] = useLexicalComposerContext();

  if (!editor) return null;

  return (
    <div className="flex flex-col gap-5">
      <Toolbar />
      <div className="w-full flex gap-6 bg-background pl-6 py-1 rounded-lg justify-between">
        <RichTextPlugin
          contentEditable={<ContentEditable className="editor-input" />}
          ErrorBoundary={LexicalErrorBoundary}
        />

        <OverallScore
          onApply={onApply}
          onHighlight={onHighlight}
          onRescore={onRescore}
          evaluateData={evaluateData}
          suggestions={suggestions}
          setSuggestions={setSuggestions}
        />
      </div>
    </div>
  );
};

export default PreviewFinish;
