import * as React from "react";

import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

const FONT_SIZES = [
  "6px",
  "8px",
  "10px",
  "12px",
  "14px",
  "16px",
  "18px",
  "20px",
  "24px",
  "28px",
  "32px",
  "36px",
  "40px",
];

interface SelectFontSizeProps {
  value: string;
  onChange: (fontSize: string) => void;
}

const SelectFontSize: React.FC<SelectFontSizeProps> = ({ value, onChange }) => {
  const handleFontSizeChange = (fontSize: string) => {
    onChange(fontSize);
  };

  return (
    <Select value={value} onValueChange={handleFontSizeChange}>
      <SelectTrigger className="w-[100px] focus:ring-0 focus:ring-offset-0">
        <SelectValue placeholder="Font size" />
      </SelectTrigger>
      <SelectContent>
        <SelectGroup>
          {FONT_SIZES.map((fontSize) => (
            <SelectItem key={fontSize} value={fontSize}>
              {fontSize}
            </SelectItem>
          ))}
        </SelectGroup>
      </SelectContent>
    </Select>
  );
};

export default SelectFontSize;
