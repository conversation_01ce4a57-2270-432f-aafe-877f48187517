import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import React from "react";

const FONTS = [
  { name: "Inter", value: "var(--font-inter)" },
  { name: "<PERSON><PERSON>", value: "var(--font-roboto)" },
  { name: "Open Sans", value: "var(--font-open-sans)" },
  { name: "Montserrat", value: "var(--font-montserrat)" },
  { name: "Lato", value: "var(--font-lato)" },
  { name: "Poppins", value: "var(--font-poppins)" },
  { name: "SF Pro", value: "var(--font-sfpro)" },
];

interface SelectFontFamilyProps {
  value: string;
  onChange: (fontFamily: string) => void;
}

const SelectFontFamily: React.FC<SelectFontFamilyProps> = ({
  value,
  onChange,
}) => {
  const handleFontFamilyChange = (fontFamily: string) => {
    onChange(fontFamily);
  };

  return (
    <Select value={value} onValueChange={handleFontFamilyChange}>
      <SelectTrigger className="w-[160px] focus:ring-0 focus:ring-offset-0">
        <SelectValue placeholder="Font size" />
      </SelectTrigger>
      <SelectContent>
        <SelectGroup>
          {FONTS.map((font) => (
            <SelectItem
              key={font.value}
              value={font.value}
              style={{ fontFamily: font.value }}
            >
              {font.name}
            </SelectItem>
          ))}
        </SelectGroup>
      </SelectContent>
    </Select>
  );
};

export default SelectFontFamily;
