import { Separator } from "@/components/ui/separator";
import ToolbarIcon from "../../images";
import SelectColor from "./select-color";
import SelectFontFamily from "./select-font-family";
import SelectFontSize from "./select-font-size";
import { Undo2 } from "lucide-react";
import { cn } from "@/lib/utils";
import { Redo2 } from "lucide-react";
import { useLexicalComposerContext } from "@lexical/react/LexicalComposerContext";
import { useCallback, useEffect, useRef, useState } from "react";
import {
  $getSelection,
  $isRangeSelection,
  CAN_REDO_COMMAND,
  CAN_UNDO_COMMAND,
  FORMAT_ELEMENT_COMMAND,
  FORMAT_TEXT_COMMAND,
  REDO_COMMAND,
  SELECTION_CHANGE_COMMAND,
  TextNode,
  UNDO_COMMAND,
} from "lexical";
import {
  $isListNode,
  INSERT_ORDERED_LIST_COMMAND,
  INSERT_UNORDERED_LIST_COMMAND,
  REMOVE_LIST_COMMAND,
} from "@lexical/list";
import { $findMatchingParent, mergeRegister } from "@lexical/utils";
import { useHistoryEditorState } from "@/lib/store/history-editor-store";

const LowPriority = 1;

const Toolbar = () => {
  const [editor] = useLexicalComposerContext();
  const editorState = useHistoryEditorState();
  const setEditorState = editorState.setEditorState;
  const toolbarRef = useRef(null);

  const [canUndo, setCanUndo] = useState<boolean>(editorState.canUndo);
  const [canRedo, setCanRedo] = useState<boolean>(editorState.canRedo);
  const [isBold, setIsBold] = useState<boolean>(editorState.isBold);
  const [isItalic, setIsItalic] = useState<boolean>(editorState.isItalic);
  const [isUnderline, setIsUnderline] = useState<boolean>(
    editorState.isUnderline
  );
  const [fontSize, setFontSize] = useState<string>(editorState.fontSize);
  const [fontFamily, setFontFamily] = useState<string>(editorState.fontFamily);
  const [fontColor, setFontColor] = useState<string>(editorState.fontColor);
  const [isOrderedList, setIsOrderedList] = useState<boolean>(
    editorState.isOrderedList
  );
  const [isUnorderedList, setIsUnorderedList] = useState<boolean>(
    editorState.isUnorderedList
  );

  // // Khôi phục editor state khi component mount
  // useEffect(() => {
  //   if (editor) {
  //     // Khôi phục trạng thái undo/redo
  //     editor.dispatchCommand(CAN_UNDO_COMMAND, editorState.canUndo);
  //     editor.dispatchCommand(CAN_REDO_COMMAND, editorState.canRedo);
  //   }
  // }, [editor, editorState]);

  const $updateToolbar = useCallback(() => {
    const selection = $getSelection();
    if ($isRangeSelection(selection)) {
      // Update text format
      setIsBold(selection.hasFormat("bold"));
      setIsItalic(selection.hasFormat("italic"));
      setIsUnderline(selection.hasFormat("underline"));

      // Get font size, font family, and color
      const node = selection.getNodes()[0];
      if (node instanceof TextNode) {
        // Check style attribute to get font size and color
        const styleString = node.getStyle();
        if (styleString) {
          // Extract font size
          const fontSizeMatch = styleString.match(/font-size:\s*([^;]+)/);
          if (fontSizeMatch && fontSizeMatch[1]) {
            setFontSize(fontSizeMatch[1]);
          } else {
            setFontSize("12px"); // Default
          }

          // Extract font family
          const fontFamilyMatch = styleString.match(/font-family:\s*([^;]+)/);
          if (fontFamilyMatch && fontFamilyMatch[1]) {
            setFontFamily(fontFamilyMatch[1]);
          } else {
            setFontFamily("var(--font-inter)"); // Default
          }

          // Extract color
          const colorMatch = styleString.match(/color:\s*([^;]+)/);
          if (colorMatch && colorMatch[1]) {
            setFontColor(colorMatch[1]);
          } else {
            setFontColor("#06042B"); // Default black
          }
        }
      }

      // Kiểm tra danh sách bằng $findMatchingParent để tìm node cha gần nhất khớp với điều kiện
      const anchorNode = selection.anchor.getNode();
      const listNode = $findMatchingParent(anchorNode, $isListNode);

      if (listNode && $isListNode(listNode)) {
        const listType = listNode.getListType();
        setIsOrderedList(listType === "number");
        setIsUnorderedList(listType === "bullet");
      } else {
        setIsOrderedList(false);
        setIsUnorderedList(false);
      }
    }
  }, []);

  // Cập nhật editor state khi có thay đổi
  useEffect(() => {
    setEditorState({
      canUndo,
      canRedo,
      isBold,
      isItalic,
      isUnderline,
      fontSize,
      fontFamily,
      fontColor,
      isOrderedList,
      isUnorderedList,
    });
  }, [
    canUndo,
    canRedo,
    isBold,
    isItalic,
    isUnderline,
    fontSize,
    fontFamily,
    fontColor,
    isOrderedList,
    isUnorderedList,
    setEditorState,
  ]);

  useEffect(() => {
    return mergeRegister(
      editor.registerUpdateListener(({ editorState }) => {
        editorState.read(() => {
          $updateToolbar();
        });
      }),
      editor.registerCommand(
        SELECTION_CHANGE_COMMAND,
        (_payload, _newEditor) => {
          $updateToolbar();
          return false;
        },
        LowPriority
      ),
      editor.registerCommand(
        CAN_UNDO_COMMAND,
        (payload) => {
          setCanUndo(payload);
          return false;
        },
        LowPriority
      ),
      editor.registerCommand(
        CAN_REDO_COMMAND,
        (payload) => {
          setCanRedo(payload);
          return false;
        },
        LowPriority
      )
    );
  }, [editor, $updateToolbar]);

  const onFontSizeChange = useCallback(
    (fontSize: string) => {
      editor.update(() => {
        const selection = $getSelection();
        if ($isRangeSelection(selection)) {
          selection.getNodes().forEach((node) => {
            if (node instanceof TextNode) {
              const currentStyle = node.getStyle() || "";
              // Kiểm tra xem style hiện tại đã có font-size chưa
              const fontSizePattern = /font-size:\s*([^;]+)(;|$)/;
              let newStyle;

              if (fontSizePattern.test(currentStyle)) {
                // Nếu đã có font-size, thay thế nó
                newStyle = currentStyle.replace(
                  fontSizePattern,
                  `font-size: ${fontSize}$2`
                );
              } else {
                // Nếu chưa có, thêm mới
                newStyle =
                  currentStyle +
                  (currentStyle && !currentStyle.endsWith(";") ? ";" : "") +
                  `font-size: ${fontSize};`;
              }

              node.setStyle(newStyle);
            }
          });
        }
      });
    },
    [editor]
  );

  const onFontFamilyChange = useCallback(
    (fontFamily: string) => {
      editor.update(() => {
        const selection = $getSelection();
        if ($isRangeSelection(selection)) {
          selection.getNodes().forEach((node) => {
            if (node instanceof TextNode) {
              const currentStyle = node.getStyle() || "";
              // Kiểm tra xem style hiện tại đã có font-family chưa
              const fontFamilyPattern = /font-family:\s*([^;]+)(;|$)/;
              let newStyle;

              if (fontFamilyPattern.test(currentStyle)) {
                // Nếu đã có font-family, thay thế nó
                newStyle = currentStyle.replace(
                  fontFamilyPattern,
                  `font-family: ${fontFamily}$2`
                );
              } else {
                // Nếu chưa có, thêm mới
                newStyle =
                  currentStyle +
                  (currentStyle && !currentStyle.endsWith(";") ? ";" : "") +
                  `font-family: ${fontFamily};`;
              }

              node.setStyle(newStyle);
            }
          });
        }
      });
    },
    [editor]
  );

  const onFontColorChange = useCallback(
    (color: string) => {
      editor.update(() => {
        const selection = $getSelection();
        if ($isRangeSelection(selection)) {
          selection.getNodes().forEach((node) => {
            if (node instanceof TextNode) {
              const currentStyle = node.getStyle() || "";
              // Kiểm tra xem style hiện tại đã có color chưa
              const colorPattern = /color:\s*([^;]+)(;|$)/;
              let newStyle;

              if (colorPattern.test(currentStyle)) {
                // Nếu đã có color, thay thế nó
                newStyle = currentStyle.replace(
                  colorPattern,
                  `color: ${color}$2`
                );
              } else {
                // Nếu chưa có, thêm mới
                newStyle =
                  currentStyle +
                  (currentStyle && !currentStyle.endsWith(";") ? ";" : "") +
                  `color: ${color};`;
              }

              node.setStyle(newStyle);
            }
          });
        }
      });
    },
    [editor]
  );

  return (
    <div
      className="px-6 py-4 rounded-xl bg-background flex items-center gap-2"
      ref={toolbarRef}
    >
      {/* Font family */}
      <SelectFontFamily
        value={fontFamily}
        onChange={(fontFamily) => onFontFamilyChange(fontFamily)}
      />

      {/* Font size */}
      <SelectFontSize
        value={fontSize}
        onChange={(fontSize) => onFontSizeChange(fontSize)}
      />

      {/* Color */}
      <SelectColor
        value={fontColor}
        onChange={(color) => onFontColorChange(color)}
      />

      {/* Text style */}
      <ToolbarIcon
        isActive={isUnderline}
        name="underline"
        onClick={() => {
          editor.dispatchCommand(FORMAT_TEXT_COMMAND, "underline");
        }}
      />
      <ToolbarIcon
        isActive={isItalic}
        name="italic"
        onClick={() => {
          editor.dispatchCommand(FORMAT_TEXT_COMMAND, "italic");
        }}
      />
      <ToolbarIcon
        isActive={isBold}
        name="bold"
        onClick={() => {
          editor.dispatchCommand(FORMAT_TEXT_COMMAND, "bold");
        }}
      />

      <Separator className="bg-[#E0E2E7] h-5" orientation="vertical" />

      {/* List */}
      <ToolbarIcon
        isActive={isUnorderedList}
        name="list-bullet"
        onClick={() => {
          if (isUnorderedList) {
            // Nếu đã là unordered list, khi click lại sẽ remove list
            editor.dispatchCommand(REMOVE_LIST_COMMAND, undefined);
          } else {
            // Nếu chưa phải unordered list hoặc là ordered list, chuyển sang unordered list
            editor.dispatchCommand(INSERT_UNORDERED_LIST_COMMAND, undefined);
          }
        }}
      />
      <ToolbarIcon
        isActive={isOrderedList}
        name="list-number"
        onClick={() => {
          if (isOrderedList) {
            // Nếu đã là ordered list, khi click lại sẽ remove list
            editor.dispatchCommand(REMOVE_LIST_COMMAND, undefined);
          } else {
            // Nếu chưa phải ordered list hoặc là unordered list, chuyển sang ordered list
            editor.dispatchCommand(INSERT_ORDERED_LIST_COMMAND, undefined);
          }
        }}
      />
      <Separator className="bg-[#E0E2E7] h-5" orientation="vertical" />

      {/* Text align */}
      <ToolbarIcon
        name="align-left"
        onClick={() => {
          editor.dispatchCommand(FORMAT_ELEMENT_COMMAND, "left");
        }}
      />
      <ToolbarIcon
        name="align-center"
        onClick={() => {
          editor.dispatchCommand(FORMAT_ELEMENT_COMMAND, "center");
        }}
      />
      <ToolbarIcon
        name="align-right"
        onClick={() => {
          editor.dispatchCommand(FORMAT_ELEMENT_COMMAND, "right");
        }}
      />
      <ToolbarIcon
        name="align-justify"
        onClick={() => {
          editor.dispatchCommand(FORMAT_ELEMENT_COMMAND, "justify");
        }}
      />

      <Separator className="bg-[#E0E2E7] h-5" orientation="vertical" />

      <Undo2
        className={cn(
          "cursor-pointer h-6 w-6",
          !canUndo && "opacity-50 cursor-not-allowed pointer-events-none"
        )}
        onClick={() => {
          editor.dispatchCommand(UNDO_COMMAND, undefined);
        }}
      />
      <Redo2
        className={cn(
          "cursor-pointer h-6 w-6",
          !canRedo && "opacity-50 cursor-not-allowed pointer-events-none"
        )}
        onClick={() => {
          editor.dispatchCommand(REDO_COMMAND, undefined);
        }}
      />
    </div>
  );
};

export default Toolbar;
