"use client";

import * as React from "react";

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuRadioGroup,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { cn } from "@/lib/utils";
import { Check, ChevronDown } from "lucide-react";

const COLORS = [
  "#06042B",
  "#7E77F8",
  "#3B82F6",
  "#22B8CF",
  "#18B953",
  "#FFEC18",
  "#EF4444",
  "#F06595",
  "#CED4DA",
  "#655FC6",
  "#1D4ED8",
  "#0B7285",
  "#15803D",
  "#FFB706",
  "#B91C1C",
  "#C2255C",
];

interface SelectColorProps {
  value: string;
  onChange: (color: string) => void;
}

const SelectColor: React.FC<SelectColorProps> = ({ value, onChange }) => {
  const [open, setOpen] = React.useState(false);

  const handleColorChange = (color: string) => {
    onChange(color);
  };

  return (
    <DropdownMenu open={open} onOpenChange={setOpen}>
      <DropdownMenuTrigger asChild>
        <div className="flex items-center cursor-pointer">
          <div
            className="w-6 h-6 rounded-sm"
            style={{ backgroundColor: value }}
          />
          <ChevronDown
            className={cn(
              "h-4 w-4 shrink-0 opacity-50 transition-transform duration-200",
              open && "rotate-180"
            )}
          />
        </div>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="start">
        <DropdownMenuRadioGroup
          value={value}
          onValueChange={onChange}
          className="grid grid-cols-8 gap-1"
        >
          {COLORS.map((item) => (
            <DropdownMenuGroup key={item}>
              <DropdownMenuItem className="px-0 py-0">
                <div
                  className="w-6 h-6 rounded-sm relative cursor-pointer"
                  style={{ backgroundColor: item }}
                  onClick={() => handleColorChange(item)}
                >
                  {value === item && (
                    <Check
                      className="w-4 h-4 absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2"
                      color="white"
                    />
                  )}
                </div>
              </DropdownMenuItem>
            </DropdownMenuGroup>
          ))}
        </DropdownMenuRadioGroup>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default SelectColor;
