import React from "react";

const ListBullets = () => {
  return (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M19.5 3H4.5C4.10218 3 3.72064 3.15804 3.43934 3.43934C3.15804 3.72064 3 4.10218 3 4.5V19.5C3 19.8978 3.15804 20.2794 3.43934 20.5607C3.72064 20.842 4.10218 21 4.5 21H19.5C19.8978 21 20.2794 20.842 20.5607 20.5607C20.842 20.2794 21 19.8978 21 19.5V4.5C21 4.10218 20.842 3.72064 20.5607 3.43934C20.2794 3.15804 19.8978 3 19.5 3ZM6.375 17.625C6.1525 17.625 5.93499 17.559 5.74998 17.4354C5.56498 17.3118 5.42078 17.1361 5.33564 16.9305C5.25049 16.725 5.22821 16.4988 5.27162 16.2805C5.31502 16.0623 5.42217 15.8618 5.5795 15.7045C5.73684 15.5472 5.93729 15.44 6.15552 15.3966C6.37375 15.3532 6.59995 15.3755 6.80552 15.4606C7.01109 15.5458 7.18679 15.69 7.3104 15.875C7.43402 16.06 7.5 16.2775 7.5 16.5C7.5 16.7984 7.38147 17.0845 7.1705 17.2955C6.95952 17.5065 6.67337 17.625 6.375 17.625ZM6.375 13.125C6.1525 13.125 5.93499 13.059 5.74998 12.9354C5.56498 12.8118 5.42078 12.6361 5.33564 12.4305C5.25049 12.225 5.22821 11.9988 5.27162 11.7805C5.31502 11.5623 5.42217 11.3618 5.5795 11.2045C5.73684 11.0472 5.93729 10.94 6.15552 10.8966C6.37375 10.8532 6.59995 10.8755 6.80552 10.9606C7.01109 11.0458 7.18679 11.19 7.3104 11.375C7.43402 11.56 7.5 11.7775 7.5 12C7.5 12.2984 7.38147 12.5845 7.1705 12.7955C6.95952 13.0065 6.67337 13.125 6.375 13.125ZM6.375 8.625C6.1525 8.625 5.93499 8.55902 5.74998 8.4354C5.56498 8.31179 5.42078 8.13609 5.33564 7.93052C5.25049 7.72495 5.22821 7.49875 5.27162 7.28052C5.31502 7.06229 5.42217 6.86184 5.5795 6.7045C5.73684 6.54717 5.93729 6.44002 6.15552 6.39662C6.37375 6.35321 6.59995 6.37549 6.80552 6.46064C7.01109 6.54578 7.18679 6.68998 7.3104 6.87498C7.43402 7.05999 7.5 7.2775 7.5 7.5C7.5 7.79837 7.38147 8.08452 7.1705 8.2955C6.95952 8.50647 6.67337 8.625 6.375 8.625ZM18 17.25H9.75C9.55109 17.25 9.36032 17.171 9.21967 17.0303C9.07902 16.8897 9 16.6989 9 16.5C9 16.3011 9.07902 16.1103 9.21967 15.9697C9.36032 15.829 9.55109 15.75 9.75 15.75H18C18.1989 15.75 18.3897 15.829 18.5303 15.9697C18.671 16.1103 18.75 16.3011 18.75 16.5C18.75 16.6989 18.671 16.8897 18.5303 17.0303C18.3897 17.171 18.1989 17.25 18 17.25ZM18 12.75H9.75C9.55109 12.75 9.36032 12.671 9.21967 12.5303C9.07902 12.3897 9 12.1989 9 12C9 11.8011 9.07902 11.6103 9.21967 11.4697C9.36032 11.329 9.55109 11.25 9.75 11.25H18C18.1989 11.25 18.3897 11.329 18.5303 11.4697C18.671 11.6103 18.75 11.8011 18.75 12C18.75 12.1989 18.671 12.3897 18.5303 12.5303C18.3897 12.671 18.1989 12.75 18 12.75ZM18 8.25H9.75C9.55109 8.25 9.36032 8.17098 9.21967 8.03033C9.07902 7.88968 9 7.69891 9 7.5C9 7.30109 9.07902 7.11032 9.21967 6.96967C9.36032 6.82902 9.55109 6.75 9.75 6.75H18C18.1989 6.75 18.3897 6.82902 18.5303 6.96967C18.671 7.11032 18.75 7.30109 18.75 7.5C18.75 7.69891 18.671 7.88968 18.5303 8.03033C18.3897 8.17098 18.1989 8.25 18 8.25Z"
        fill="#667085"
      />
    </svg>
  );
};

export default ListBullets;
