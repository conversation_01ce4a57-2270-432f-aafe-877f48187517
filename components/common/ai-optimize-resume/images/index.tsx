import React, { ComponentProps } from "react";
import TextUnderline from "./text-underline";
import TextItalic from "./text-italic";
import TextBold from "./text-bold";
import ListBullets from "./list-bullets";
import ListNumber from "./list-number";
import AlignLeft from "./align-left";
import AlignCenter from "./align-center";
import AlignRight from "./align-right";
import AlignJustify from "./align-justify";
import { cn } from "@/lib/utils";

type ToolbarIconName =
  | "underline"
  | "italic"
  | "bold"
  | "list-bullet"
  | "list-number"
  | "align-left"
  | "align-center"
  | "align-right"
  | "align-justify";

const SVGS: Record<ToolbarIconName, React.ReactNode> = {
  underline: <TextUnderline />,
  italic: <TextItalic />,
  bold: <TextBold />,
  "list-bullet": <ListBullets />,
  "list-number": <ListNumber />,
  "align-left": <AlignLeft />,
  "align-center": <AlignCenter />,
  "align-right": <AlignRight />,
  "align-justify": <AlignJustify />,
};

interface ToolbarIconProps extends ComponentProps<"div"> {
  name: ToolbarIconName;
  isActive?: boolean;
  disabled?: boolean;
}

const ToolbarIcon: React.FC<ToolbarIconProps> = ({
  name,
  isActive,
  disabled,
  className,
  ...rest
}) => {
  return (
    <div
      className={cn(
        "cursor-pointer rounded-sm border border-background hover:border-gray-200 hover:bg-slate-200",
        isActive ? "border-gray-200 bg-slate-200" : "",
        disabled
          ? "opacity-50 cursor-not-allowed hover:border-background hover:bg-background"
          : "",
        className
      )}
      {...rest}
    >
      {SVGS[name]}
    </div>
  );
};

export default ToolbarIcon;
