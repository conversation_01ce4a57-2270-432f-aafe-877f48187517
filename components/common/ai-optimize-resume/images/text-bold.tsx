import React from "react";

const TextBold = () => {
  return (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M15.75 14.625C15.75 15.1223 15.5525 15.5992 15.2008 15.9508C14.8492 16.3025 14.3723 16.5 13.875 16.5H9V12.75H13.875C14.3723 12.75 14.8492 12.9475 15.2008 13.2992C15.5525 13.6508 15.75 14.1277 15.75 14.625ZM21 4.5V19.5C21 19.8978 20.842 20.2794 20.5607 20.5607C20.2794 20.842 19.8978 21 19.5 21H4.5C4.10218 21 3.72064 20.842 3.43934 20.5607C3.15804 20.2794 3 19.8978 3 19.5V4.5C3 4.10218 3.15804 3.72064 3.43934 3.43934C3.72064 3.15804 4.10218 3 4.5 3H19.5C19.8978 3 20.2794 3.15804 20.5607 3.43934C20.842 3.72064 21 4.10218 21 4.5ZM17.25 14.625C17.2496 14.033 17.0934 13.4515 16.7972 12.9389C16.501 12.4263 16.0752 12.0007 15.5625 11.7047C16.0183 11.2269 16.324 10.6258 16.4416 9.97602C16.5592 9.32621 16.4836 8.65617 16.2241 8.04891C15.9646 7.44164 15.5327 6.92385 14.9818 6.55965C14.431 6.19546 13.7854 6.00087 13.125 6H8.25C8.05109 6 7.86032 6.07902 7.71967 6.21967C7.57902 6.36032 7.5 6.55109 7.5 6.75V17.25C7.5 17.4489 7.57902 17.6397 7.71967 17.7803C7.86032 17.921 8.05109 18 8.25 18H13.875C14.7701 18 15.6285 17.6444 16.2615 17.0115C16.8944 16.3785 17.25 15.5201 17.25 14.625ZM15 9.375C15 8.87772 14.8025 8.40081 14.4508 8.04917C14.0992 7.69754 13.6223 7.5 13.125 7.5H9V11.25H13.125C13.6223 11.25 14.0992 11.0525 14.4508 10.7008C14.8025 10.3492 15 9.87228 15 9.375Z"
        fill="#667085"
      />
    </svg>
  );
};

export default TextBold;
