import React from "react";

const TextUnderline = () => {
  return (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M19.5 3H4.5C4.10218 3 3.72064 3.15804 3.43934 3.43934C3.15804 3.72064 3 4.10218 3 4.5V19.5C3 19.8978 3.15804 20.2794 3.43934 20.5607C3.72064 20.842 4.10218 21 4.5 21H19.5C19.8978 21 20.2794 20.842 20.5607 20.5607C20.842 20.2794 21 19.8978 21 19.5V4.5C21 4.10218 20.842 3.72064 20.5607 3.43934C20.2794 3.15804 19.8978 3 19.5 3ZM7.5 6.75C7.5 6.55109 7.57902 6.36032 7.71967 6.21967C7.86032 6.07902 8.05109 6 8.25 6C8.44891 6 8.63968 6.07902 8.78033 6.21967C8.92098 6.36032 9 6.55109 9 6.75V11.25C9 12.0456 9.31607 12.8087 9.87868 13.3713C10.4413 13.9339 11.2044 14.25 12 14.25C12.7956 14.25 13.5587 13.9339 14.1213 13.3713C14.6839 12.8087 15 12.0456 15 11.25V6.75C15 6.55109 15.079 6.36032 15.2197 6.21967C15.3603 6.07902 15.5511 6 15.75 6C15.9489 6 16.1397 6.07902 16.2803 6.21967C16.421 6.36032 16.5 6.55109 16.5 6.75V11.25C16.5 12.4435 16.0259 13.5881 15.182 14.432C14.3381 15.2759 13.1935 15.75 12 15.75C10.8065 15.75 9.66193 15.2759 8.81802 14.432C7.97411 13.5881 7.5 12.4435 7.5 11.25V6.75ZM16.5 18.75H7.5C7.30109 18.75 7.11032 18.671 6.96967 18.5303C6.82902 18.3897 6.75 18.1989 6.75 18C6.75 17.8011 6.82902 17.6103 6.96967 17.4697C7.11032 17.329 7.30109 17.25 7.5 17.25H16.5C16.6989 17.25 16.8897 17.329 17.0303 17.4697C17.171 17.6103 17.25 17.8011 17.25 18C17.25 18.1989 17.171 18.3897 17.0303 18.5303C16.8897 18.671 16.6989 18.75 16.5 18.75Z"
        fill="#667085"
      />
    </svg>
  );
};

export default TextUnderline;
