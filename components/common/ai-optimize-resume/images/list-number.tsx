import React from "react";

const ListNumber = () => {
  return (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M19.5 3H4.5C4.10218 3 3.72064 3.15804 3.43934 3.43934C3.15804 3.72064 3 4.10218 3 4.5V19.5C3 19.8978 3.15804 20.2794 3.43934 20.5607C3.72064 20.842 4.10218 21 4.5 21H19.5C19.8978 21 20.2794 20.842 20.5607 20.5607C20.842 20.2794 21 19.8978 21 19.5V4.5C21 4.10218 20.842 3.72064 20.5607 3.43934C20.2794 3.15804 19.8978 3 19.5 3ZM5.32875 7.08563C5.28461 6.99749 5.25828 6.90151 5.25126 6.80319C5.24424 6.70487 5.25667 6.60613 5.28784 6.51261C5.31901 6.4191 5.36831 6.33265 5.43292 6.2582C5.49753 6.18376 5.57618 6.12278 5.66437 6.07875L7.16437 5.32875C7.27876 5.27151 7.40589 5.24448 7.53367 5.25022C7.66145 5.25596 7.78563 5.29429 7.89442 5.36155C8.00322 5.42882 8.093 5.52279 8.15523 5.63454C8.21747 5.74629 8.25009 5.87209 8.25 6V10.5C8.25 10.6989 8.17098 10.8897 8.03033 11.0303C7.88968 11.171 7.69891 11.25 7.5 11.25C7.30109 11.25 7.11032 11.171 6.96967 11.0303C6.82902 10.8897 6.75 10.6989 6.75 10.5V7.21313L6.33563 7.42125C6.24749 7.46539 6.15151 7.49172 6.05319 7.49874C5.95487 7.50576 5.85613 7.49333 5.76261 7.46216C5.6691 7.43099 5.58265 7.38169 5.5082 7.31708C5.43376 7.25247 5.37278 7.17382 5.32875 7.08563ZM8.625 16.875C8.82391 16.875 9.01468 16.954 9.15533 17.0947C9.29598 17.2353 9.375 17.4261 9.375 17.625C9.375 17.8239 9.29598 18.0147 9.15533 18.1553C9.01468 18.296 8.82391 18.375 8.625 18.375H6.375C6.23572 18.375 6.09918 18.3362 5.9807 18.263C5.86222 18.1898 5.76647 18.085 5.70418 17.9604C5.64189 17.8358 5.61552 17.6964 5.62803 17.5576C5.64054 17.4189 5.69143 17.2864 5.775 17.175L7.80656 14.4666C7.85167 14.4034 7.87563 14.3276 7.875 14.25C7.87485 14.1616 7.84349 14.0761 7.78646 14.0086C7.72942 13.9411 7.65038 13.8959 7.56327 13.881C7.47616 13.8661 7.38658 13.8824 7.31034 13.9271C7.2341 13.9718 7.1761 14.042 7.14656 14.1253C7.08018 14.3129 6.94198 14.4665 6.76239 14.5522C6.58279 14.6379 6.37651 14.6487 6.18891 14.5823C6.00131 14.516 5.84776 14.3778 5.76205 14.1982C5.67634 14.0186 5.66549 13.8123 5.73187 13.6247C5.82289 13.3721 5.9671 13.142 6.15475 12.9499C6.3424 12.7579 6.5691 12.6083 6.81954 12.5115C7.06997 12.4146 7.33828 12.3727 7.60632 12.3885C7.87436 12.4043 8.13588 12.4775 8.37318 12.6032C8.61048 12.7288 8.81803 12.9039 8.98179 13.1167C9.14555 13.3295 9.26169 13.575 9.32237 13.8366C9.38304 14.0981 9.38683 14.3697 9.33348 14.6328C9.28013 14.896 9.17088 15.1446 9.01312 15.3619L7.875 16.875H8.625ZM18 17.25H11.25C11.0511 17.25 10.8603 17.171 10.7197 17.0303C10.579 16.8897 10.5 16.6989 10.5 16.5C10.5 16.3011 10.579 16.1103 10.7197 15.9697C10.8603 15.829 11.0511 15.75 11.25 15.75H18C18.1989 15.75 18.3897 15.829 18.5303 15.9697C18.671 16.1103 18.75 16.3011 18.75 16.5C18.75 16.6989 18.671 16.8897 18.5303 17.0303C18.3897 17.171 18.1989 17.25 18 17.25ZM18 12.75H11.25C11.0511 12.75 10.8603 12.671 10.7197 12.5303C10.579 12.3897 10.5 12.1989 10.5 12C10.5 11.8011 10.579 11.6103 10.7197 11.4697C10.8603 11.329 11.0511 11.25 11.25 11.25H18C18.1989 11.25 18.3897 11.329 18.5303 11.4697C18.671 11.6103 18.75 11.8011 18.75 12C18.75 12.1989 18.671 12.3897 18.5303 12.5303C18.3897 12.671 18.1989 12.75 18 12.75ZM18 8.25H11.25C11.0511 8.25 10.8603 8.17098 10.7197 8.03033C10.579 7.88968 10.5 7.69891 10.5 7.5C10.5 7.30109 10.579 7.11032 10.7197 6.96967C10.8603 6.82902 11.0511 6.75 11.25 6.75H18C18.1989 6.75 18.3897 6.82902 18.5303 6.96967C18.671 7.11032 18.75 7.30109 18.75 7.5C18.75 7.69891 18.671 7.88968 18.5303 8.03033C18.3897 8.17098 18.1989 8.25 18 8.25Z"
        fill="#667085"
      />
    </svg>
  );
};

export default ListNumber;
