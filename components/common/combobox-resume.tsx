"use client";

import * as React from "react";
import { Check, ChevronDown, Loader2 } from "lucide-react";

import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { useQuery } from "@tanstack/react-query";
import { getAllResumes } from "@/apis/resume/resume";
import { ResponseUploadResume } from "@/apis/resume/types";

interface ComboboxResumeProps {
  resume: ResponseUploadResume | null;
  onChange: (resume: ResponseUploadResume | null) => void;
}

const ComboboxResume = ({ resume, onChange }: ComboboxResumeProps) => {
  const [open, setOpen] = React.useState(false);

  const { data: allResumes, isLoading } = useQuery({
    queryKey: ["get-all-resume"],
    queryFn: getAllResumes,
  });

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant={resume ? "outline" : "secondary"}
          role="combobox"
          aria-expanded={open}
          className={cn("w-full justify-between font-normal rounded-lg")}
        >
          <p className="text-sm truncate overflow-hidden">
            {resume ? resume.resume_name : "Select resume from library"}
          </p>
          <ChevronDown
            className={cn(
              "ml-2 h-4 w-4 shrink-0 opacity-50 transition-transform duration-200",
              open && "rotate-180"
            )}
          />
        </Button>
      </PopoverTrigger>
      <PopoverContent
        className="w-full p-0"
        style={{ width: "var(--radix-popover-trigger-width)" }}
      >
        <Command>
          <CommandInput placeholder="Search resume..." />
          <CommandList>
            {isLoading ? (
              <div className="w-full flex justify-center items-center h-20 overflow-hidden">
                <Loader2 size={60} className="animate-spin" color="#7E77F8" />
              </div>
            ) : (
              <>
                <CommandEmpty>No resume found.</CommandEmpty>
                <CommandGroup>
                  {allResumes &&
                    allResumes.map((item) => (
                      <CommandItem
                        className="flex flex-row justify-between items-center gap-2 cursor-pointer"
                        key={item.id}
                        value={item.resume_name}
                        onSelect={() => {
                          onChange(item.id === resume?.id ? null : item);
                          setOpen(false);
                        }}
                      >
                        <p
                          className={cn(
                            "text-sm truncate overflow-hidden",
                            item.id === resume?.id && "!text-[#7E77F8]"
                          )}
                        >
                          {item.resume_name}
                        </p>

                        <Check
                          color="#7E77F8"
                          className={cn(
                            "mr-2 h-4 w-4",
                            item.id === resume?.id ? "opacity-100" : "opacity-0"
                          )}
                        />
                      </CommandItem>
                    ))}
                </CommandGroup>
              </>
            )}
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
};

export default ComboboxResume;
