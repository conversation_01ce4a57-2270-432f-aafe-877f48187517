"use client";

import React, { useEffect, useRef, useState } from "react";
import {
  RequestUpdateResume,
  ResponseGetAllResumes,
  ResponseUploadResume,
} from "@/apis/resume/types";
import { Document, Page, pdfjs } from "react-pdf";
import "react-pdf/dist/Page/AnnotationLayer.css";
import "react-pdf/dist/Page/TextLayer.css";
import { Loader2 } from "lucide-react";
import { isBrowser } from "react-device-detect";

import ViewResume, { useViewResume } from "./view";
import { cn } from "@/lib/utils";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { deleteResume, updateResume } from "@/apis/resume/resume";

import { timeAgo } from "@/utils/time";
import {
  AlertDeleteResume,
  DialogUpdateResume,
  ResumeActionDropdown,
} from "../resume-menu-option";
import { useRouter } from "@/i18n/navigation";
import { ROUTE } from "@/constants/route";
import { useResume } from "@/lib/store/resume-store";

pdfjs.GlobalWorkerOptions.workerSrc = `//cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjs.version}/pdf.worker.min.js`;

// pdfjs.GlobalWorkerOptions.workerSrc = new URL(
//   "pdfjs-dist/build/pdf.worker.min.mjs",
//   import.meta.url
// ).toString();

const RESUME_OPTIONS = [
  {
    image: "/images/dashboard/resume-management/view-detail.svg",
    label: "View detail",
  },
  {
    image: "/images/dashboard/resume-management/rename.svg",
    label: "Rename",
  },
  {
    image: "/images/dashboard/resume-management/numpad.svg",
    label: "Score this Resume",
  },
  {
    image: "/images/dashboard/resume-management/file-down.svg",
    label: "Download",
  },
  {
    image: "/images/dashboard/resume-management/history.svg",
    label: "Scan History",
  },
  {
    image: "/images/dashboard/resume-management/delete.svg",
    label: "Delete Resume",
  },
] as const;

type ResumeLabel = (typeof RESUME_OPTIONS)[number]["label"];

interface ViewItemProps {
  resume: ResponseUploadResume;
}

const ViewItem: React.FC<ViewItemProps> = ({ resume }) => {
  const route = useRouter();
  const { onNavigateToScore } = useResume();
  const queryClient = useQueryClient();
  const { view } = useViewResume();

  const containerRef = useRef<HTMLDivElement | null>(null);
  const [size, setSize] = useState<{ width: number; height: number }>({
    width: 1,
    height: 1,
  });

  const [openDialogDelete, setOpenDialogDelete] = useState<boolean>(false);
  const [openDialogUpdate, setOpenDialogUpdate] = useState<boolean>(false);
  const [openDetail, setOpenDetail] = useState<boolean>(false);

  const proxyUrl =
    typeof resume.file_path === "string"
      ? `/api/pdf?url=${encodeURIComponent(
          resume.download_url || resume.file_path
        )}`
      : resume.file_path;

  const { mutateAsync: deleteResumeFile } = useMutation({
    mutationFn: (id: string) => deleteResume(id),
    onMutate: async (id) => {
      // Hủy các queries đang chạy để tránh ghi đè optimistic update
      await queryClient.cancelQueries({ queryKey: ["get-all-resume"] });

      // Lưu trữ state hiện tại để có thể rollback nếu có lỗi
      const previousResumes = queryClient.getQueryData(["get-all-resume"]);

      // Cập nhật cache bằng cách loại bỏ resume cần xóa
      queryClient.setQueryData(["get-all-resume"], (old: any) => {
        return old ? old.filter((resume: any) => resume.id !== id) : [];
      });

      // Trả về context để sử dụng trong onError
      return { previousResumes };
    },
    onError: (err, id, context) => {
      // Nếu mutation thất bại, khôi phục lại dữ liệu trước đó
      if (context?.previousResumes) {
        queryClient.setQueryData(["get-all-resume"], context.previousResumes);
      }
    },
    onSettled: () => {
      // Bất kể thành công hay thất bại, luôn refetch để đồng bộ với server
      queryClient.invalidateQueries({ queryKey: ["get-all-resume"] });
    },
  });

  const { mutateAsync: mutateUpdateResume } = useMutation({
    mutationFn: (request: RequestUpdateResume) => updateResume(request),
    onMutate: async ({ id, resume_name }) => {
      // Hủy các queries đang chạy để tránh ghi đè optimistic update
      await queryClient.cancelQueries({ queryKey: ["get-all-resume"] });

      // Lưu trữ state hiện tại để có thể rollback nếu có lỗi
      const previousResumes = queryClient.getQueryData(["get-all-resume"]);

      // Cập nhật cache bằng cách loại bỏ resume cần xóa
      queryClient.setQueryData(
        ["get-all-resume"],
        (old: ResponseGetAllResumes) => {
          return old.map((resume) =>
            resume.id === id ? { ...resume, resume_name } : resume
          );
        }
      );

      // Trả về context để sử dụng trong onError
      return { previousResumes };
    },
    onError: (err, id, context) => {
      // Nếu mutation thất bại, khôi phục lại dữ liệu trước đó
      if (context?.previousResumes) {
        queryClient.setQueryData(["get-all-resume"], context.previousResumes);
      }
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: ["get-all-resume"] });
    },
  });

  const handleDownload = () => {
    const link = document.createElement("a");

    let fileName = resume.resume_name;

    // Ưu tiên sử dụng download_url trực tiếp cho download
    if (resume.download_url) {
      link.href = resume.download_url;
      link.download = fileName;

      // Thêm thuộc tính để buộc trình duyệt tải xuống thay vì mở file
      link.setAttribute("target", "_blank");
      link.setAttribute("rel", "noopener noreferrer");

      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } else if (typeof proxyUrl === "string") {
      link.href = proxyUrl;
      link.download = fileName;

      // Thêm thuộc tính để buộc trình duyệt tải xuống thay vì mở file
      link.setAttribute("target", "_blank");
      link.setAttribute("rel", "noopener noreferrer");

      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } else {
      const url = URL.createObjectURL(proxyUrl);
      link.href = url;
      link.download = fileName;

      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      URL.revokeObjectURL(url);
    }
  };

  const handleMenuClick = async (action: ResumeLabel, data?: any) => {
    if (action === "Delete Resume") {
      await deleteResumeFile(resume.id);
      setOpenDetail(false);
      return;
    }
    if (action === "Download") return handleDownload();
    if (action === "View detail") return setOpenDetail(true);
    if (action === "Rename" && data?.resume_name) {
      setOpenDialogUpdate(false);
      await mutateUpdateResume({
        id: resume.id,
        resume_name: data.resume_name,
      });
      return;
    }
    if (action === "Score this Resume") {
      onNavigateToScore(resume);
      route.push(ROUTE.RESUME_SCORE);
      return;
    }
  };

  const handleOnClick = () => {
    if (isBrowser) setOpenDetail(true);
  };

  //
  //
  //

  const setPdfSize = () => {
    if (containerRef && containerRef.current) {
      setSize({
        width: containerRef.current.getBoundingClientRect().width,
        height: containerRef.current.getBoundingClientRect().height,
      });
    }
  };

  useEffect(() => {
    window.addEventListener("resize", setPdfSize);
    setPdfSize();
    return () => {
      window.removeEventListener("resize", setPdfSize);
    };
  }, []);

  //
  //
  //

  if (resume?.isLoading) {
    return (
      <div className="relative w-full h-full bg-background flex flex-col overflow-hidden rounded-lg">
        <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2">
          <Loader2 size={40} className="animate-spin" color="#7E77F8" />
        </div>
      </div>
    );
  }

  if (openDetail) {
    return (
      <ViewResume.Detail
        resume={resume}
        onClose={() => setOpenDetail(false)}
        onClick={(action) => handleMenuClick(action as ResumeLabel)}
      />
    );
  }

  return (
    <div
      onClick={(e) => {
        e.stopPropagation();
        e.preventDefault();
        handleOnClick();
      }}
      className="relative w-full h-full bg-background flex flex-col overflow-hidden rounded-lg cursor-pointer"
    >
      {view === "column" ? (
        <div
          ref={containerRef}
          className="relative flex-1 w-full overflow-hidden"
        >
          <Document
            file={proxyUrl}
            loading={
              <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2">
                <Loader2 size={40} className="animate-spin" color="#7E77F8" />
              </div>
            }
          >
            <Page height={size.height} width={size.width} pageNumber={1} />
          </Document>
        </div>
      ) : null}

      <div className="flex justify-between items-center w-full gap-1 p-2">
        <div
          className={cn(
            "flex flex-col flex-1 min-w-0",
            view === "row" ? "gap-1 px-4 py-2" : "gap-0"
          )}
        >
          <p
            className={cn(
              "text-black text-sm truncate overflow-hidden",
              view === "row" ? "font-bold" : "font-normal"
            )}
          >
            {resume.resume_name}
          </p>
          <p className="text-[#667085] text-xs truncate overflow-hidden">
            {timeAgo(resume.created_at)}
          </p>
        </div>
        <ResumeActionDropdown
          options={RESUME_OPTIONS.map((option) => ({
            ...option,
            onClick:
              option.label === "Delete Resume"
                ? () => setOpenDialogDelete(true)
                : option.label === "Rename"
                ? () => setOpenDialogUpdate(true)
                : undefined,
          }))}
          onOptionClick={(action) => handleMenuClick(action as ResumeLabel)}
        />
        <AlertDeleteResume
          openDialogDelete={openDialogDelete}
          setOpenDialogDelete={setOpenDialogDelete}
          onOk={() => handleMenuClick("Delete Resume")}
        />
        <DialogUpdateResume
          name={resume.resume_name}
          openDialogUpdate={openDialogUpdate}
          setOpenDialogUpdate={setOpenDialogUpdate}
          onOk={(resume_name) => handleMenuClick("Rename", { resume_name })}
        />
      </div>
    </div>
  );
};

export default ViewItem;
