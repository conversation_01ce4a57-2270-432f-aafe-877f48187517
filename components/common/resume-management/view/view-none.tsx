import React, { ChangeEvent, useCallback, useRef, useState } from "react";
import NoResume from "../../../../public/images/dashboard/resume-management/no-resume.png";
import Image from "next/image";
import { Button } from "@/components/ui/button";

interface ViewNoneProps {
  onFileSelect: (file: File | null) => void;
}

const ViewNone: React.FC<ViewNoneProps> = ({ onFileSelect }) => {
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileChange = (e: ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      handleFile(file);
    }
  };

  const handleFile = (file: File) => {
    onFileSelect(file);
  };

  const handleDragEnter = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
  }, []);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();

    const files = e.dataTransfer.files;
    if (files.length > 0) {
      handleFile(files[0]);
    }
  }, []);

  const handleButtonClick = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    // Kích hoạt click vào input file
    fileInputRef.current?.click();
  };

  //
  //
  //

  return (
    <div className="w-full h-full flex justify-center items-center">
      <input
        type="file"
        className="hidden"
        onChange={handleFileChange}
        id="file-input"
        ref={fileInputRef}
      />

      <div className="block w-full h-full">
        <div className="h-full flex flex-col items-center justify-center">
          <label
            htmlFor="file-input"
            className="flex flex-col gap-4 items-center justify-center cursor-pointer"
            onDragEnter={handleDragEnter}
            onDragLeave={handleDragLeave}
            onDragOver={handleDragOver}
            onDrop={handleDrop}
          >
            <Image alt="No Resume" src={NoResume} />
            <p className="text-xl text-[#667085] font-semibold">
              No Resumes Yet
            </p>
            <Button
              className="text-base font-medium h-[48px] shadow-[2px_2px_16px_0px_#7E77F85E]"
              onClick={handleButtonClick}
            >
              Upload Your Resume Now
            </Button>
          </label>
        </div>
      </div>
    </div>
  );
};

export default ViewNone;
