import React, { useState } from "react";
import { cn } from "@/lib/utils";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { getAllResumes, uploadResume } from "@/apis/resume/resume";
import ViewResume, { useViewResume } from "./view";
import { ResponseGetAllResumes } from "@/apis/resume/types";
import { Loader2 } from "lucide-react";
import Loading from "@/app/[locale]/loading";

const ViewAll: React.FC = () => {
  const queryClient = useQueryClient();
  const { view } = useViewResume();
  const [file, setFile] = useState<File | null>(null);

  const {
    data: allResumes,
    isSuccess,
    isLoading,
  } = useQuery({
    queryKey: ["get-all-resume"],
    queryFn: getAllResumes,
  });

  const { mutateAsync: uploadResumeFile } = useMutation({
    mutationFn: (file: File) => uploadResume(file),
    onMutate: async (newFile: File) => {
      await queryClient.cancelQueries({ queryKey: ["get-all-resume"] });

      // Lưu trữ state hiện tại để có thể rollback nếu có lỗi
      const previousResumes = queryClient.getQueryData(["get-all-resume"]);

      // Tạo optimistic entry cho resume mới
      const optimisticResume = {
        id: `temp-${Date.now()}`,
        resume_name: newFile.name,
        created_at: new Date().toISOString(),
        file_path: newFile,
        isLoading: true,
      };

      // Cập nhật cache với optimistic data
      queryClient.setQueryData(
        ["get-all-resume"],
        (old: ResponseGetAllResumes) => {
          return old ? [optimisticResume, ...old] : [optimisticResume];
        }
      );

      // Trả về context để sử dụng trong onError
      return { previousResumes };
    },
    onError: (err, newFile, context) => {
      // Nếu mutation thất bại, khôi phục lại dữ liệu trước đó
      if (context?.previousResumes) {
        queryClient.setQueryData(["get-all-resume"], context.previousResumes);
      }
    },
    onSettled: () => {
      // Bất kể thành công hay thất bại, luôn refetch để đồng bộ với server
      setFile(null);
      queryClient.invalidateQueries({ queryKey: ["get-all-resume"] });
    },
  });

  //
  //
  //

  if (isLoading) {
    return (
      <div className="w-full h-full flex justify-center items-center">
        <Loader2 size={120} color="#7E77F8" className="animate-spin" />
      </div>
    );
  }

  if (isSuccess && allResumes?.length === 0) {
    return (
      <ViewResume.None
        onFileSelect={async (file) => {
          setFile(file);
          if (file) await uploadResumeFile(file);
        }}
      />
    );
  }

  return (
    <div
      className={cn(
        "grid gap-x-4 gap-y-5",
        view === "row"
          ? "grid-cols-1"
          : "grid-cols-2 md:grid-cols-3 custom:grid-cols-[repeat(auto-fill,minmax(260px,1fr))]"
      )}
    >
      <div
        className={
          (cn("grid-cols-1"),
          view === "column" ? "w-full h-auto aspect-[3/4]" : "")
        }
      >
        <ViewResume.Upload
          file={file}
          onFileSelect={async (file) => {
            setFile(file);
            if (file) await uploadResumeFile(file);
          }}
        />
      </div>

      {allResumes?.length &&
        allResumes.map((resume) => (
          <div
            key={resume.id}
            className={
              (cn("grid-cols-1"),
              view === "column" ? "w-full h-auto aspect-[3/4]" : "")
            }
          >
            <ViewResume.Item resume={resume} />
          </div>
        ))}
    </div>
  );
};

export default ViewAll;
