"use client";

import React, {
  createContext,
  useContext,
  useState,
  PropsWithChildren,
  useEffect,
} from "react";
import ViewAll from "./view-all";
import ViewItem from "./view-item";
import FileUpload from "./file-upload";
import ViewNone from "./view-none";
import { ViewDetail } from "./view-detail";
export type ViewResumeType = "column" | "row";
interface ViewResumeContextType {
  view: ViewResumeType;
  setView: (view: ViewResumeType) => void;
}

const ViewResumeContext = createContext<ViewResumeContextType>({
  view: "column",
  setView: () => ({}),
});

export const useViewResume = () => {
  const context = useContext(ViewResumeContext);
  if (context === undefined) {
    throw new Error("useViewResume must be used within a ViewResumeProvider");
  }
  return context;
};

interface ViewResumeProps extends React.FC<PropsWithChildren> {
  All: typeof ViewAll;
  Item: typeof ViewItem;
  Upload: typeof FileUpload;
  None: typeof ViewNone;
  Detail: typeof ViewDetail;
}

const ViewResume: ViewResumeProps = ({ children }) => {
  const [view, setView] = useState<ViewResumeType>(() => {
    if (typeof window !== "undefined") {
      const storedView = localStorage.getItem("resume-view");
      if (storedView) {
        return storedView as ViewResumeType;
      }
    }
    return "column";
  });

  useEffect(() => {
    if (typeof window !== "undefined") {
      localStorage.setItem("resume-view", view);
    }
  }, [view]);

  const value = {
    view,
    setView,
  };

  return (
    <ViewResumeContext.Provider value={value}>
      {children}
    </ViewResumeContext.Provider>
  );
};

ViewResume.All = ViewAll;
ViewResume.Item = ViewItem;
ViewResume.Upload = FileUpload;
ViewResume.None = ViewNone;
ViewResume.Detail = ViewDetail;

export default ViewResume;
