import { <PERSON><PERSON> } from "@/components/ui/button";

import { <PERSON><PERSON><PERSON>, Loader2, X } from "lucide-react";
import { ResponseUploadResume } from "@/apis/resume/types";
import { Document, Page } from "react-pdf";
import { useEffect, useRef, useState } from "react";
import { AlertDeleteResume, ResumeActionDropdown } from "../resume-menu-option";
import { useRouter } from "@/i18n/navigation";
import { ROUTE } from "@/constants/route";
import { useResume } from "@/lib/store/resume-store";
import { createPortal } from 'react-dom';
import { useMountedState } from "react-use";

const RESUME_OPTIONS = [
  {
    image: "/images/dashboard/resume-management/numpad.svg",
    label: "Score this Resume",
  },
  {
    image: "/images/dashboard/resume-management/file-down.svg",
    label: "Download",
  },
  {
    image: "/images/dashboard/resume-management/history.svg",
    label: "Scan History",
  },
  {
    image: "/images/dashboard/resume-management/delete.svg",
    label: "Delete Resume",
  },
] as const;

interface ViewDetailProps {
  resume: ResponseUploadResume;
  onClose: VoidFunction;
  onClick: (action: string) => void;
}

export const ViewDetail: React.FC<ViewDetailProps> = ({
  resume,
  onClose,
  onClick,
}) => {
  const route = useRouter();
  const { onNavigateToOptimize } = useResume();
  const containerRef = useRef<HTMLDivElement | null>(null);
  const [size, setSize] = useState<{ width: number; height: number }>({
    width: 1,
    height: 1,
  });

  const [numPages, setNumPages] = useState<number>();
  const [openDialogDelete, setOpenDialogDelete] = useState<boolean>(false);

  const proxyUrl =
    typeof resume.file_path === "string"
      ? `/api/pdf?url=${encodeURIComponent(resume.download_url || resume.file_path)}`
      : resume.file_path;

  const onDocumentLoadSuccess = ({ numPages }: { numPages: number }): void =>
    setNumPages(numPages);

  //
  //
  //

  const setPdfSize = () => {
    if (containerRef && containerRef.current) {
      setSize({
        width: containerRef.current.getBoundingClientRect().width,
        height: containerRef.current.getBoundingClientRect().height,
      });
    }
  };

  const onOptimizeResume = () => {
    onNavigateToOptimize({
      radio: "select",
      resume,
      file: null,
      jobDescription: "",
      evaluateData: null,
    });
    route.push(ROUTE.OPTIMIZE_RESUME);
  };

  
  useEffect(() => {
    window.addEventListener("resize", setPdfSize);
    setPdfSize();
    return () => {
      window.removeEventListener("resize", setPdfSize);
    };
  }, []);

  //
  //
  //

  const viewDetailContent = (
    <div className="z-50 flex flex-col absolute top-0 left-0 bottom-0 right-0 bg-black w-screen h-screen overflow-x-hidden">
      <div className="bg-[#2B2B2B] p-4 fixed top-0 left-0 right-0 z-50">
        <div className="flex items-center justify-between gap-2">
          <div className="flex items-center gap-1 flex-1 overflow-hidden">
            <X
              className="text-background cursor-pointer"
              size={20}
              onClick={onClose}
            />
            <p className="flex-1 text-background text-sm font-semibold truncate overflow-hidden">
              {resume.resume_name}
            </p>
          </div>

          <div className="flex items-center justify-end gap-1">
            <Button className="text-xs font-medium" onClick={onOptimizeResume}>
              Optimize Resume
            </Button>

            <ResumeActionDropdown
              sideOffset={20}
              triggerIcon={
                <Ellipsis className="text-background cursor-pointer" />
              }
              options={RESUME_OPTIONS.map((option) => ({
                ...option,
                onClick:
                  option.label === "Delete Resume"
                    ? () => setOpenDialogDelete(true)
                    : undefined,
              }))}
              onOptionClick={onClick}
            />
          </div>
        </div>
      </div>

      <div className="bg-[#1A1A1A] flex-1 flex flex-col items-center justify-center px-4 py-6 md:px-20 md:py-16 xl:py-16 mt-16">
        <div
          ref={containerRef}
          className="w-full flex-grow-1 h-full justify-center items-center max-w-3xl"
        >
          <Document
            file={proxyUrl}
            onLoadSuccess={onDocumentLoadSuccess}
            onLoadError={(error) => console.log("Inside Error", error)}
            loading={
              <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2">
                <Loader2 size={40} className="animate-spin" color="#7E77F8" />
              </div>
            }
          >
            {Array.apply(null, Array(numPages))
              .map((x, i) => i + 1)
              .map((page) => (
                <Page width={size.width} pageNumber={page} />
              ))}
          </Document>
        </div>
      </div>

      <AlertDeleteResume
        openDialogDelete={openDialogDelete}
        setOpenDialogDelete={setOpenDialogDelete}
        onOk={() => onClick("Delete Resume")}
      />
    </div>
  );

  return  createPortal(viewDetailContent, document.body);
};
