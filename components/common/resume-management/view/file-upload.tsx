"use client";

import { cn } from "@/lib/utils";
import { FileUp } from "lucide-react";
import React, { ChangeEvent, useCallback, useState } from "react";
import { useViewResume } from "./view";

interface FileUploadProps {
  file: File | null;
  onFileSelect: (file: File | null) => void;
}

const FileUpload: React.FC<FileUploadProps> = ({ file, onFileSelect }) => {
  const { view } = useViewResume();
  const [isDragging, setIsDragging] = useState(false);

  const handleFileChange = (e: ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      handleFile(file);
    }
  };

  const handleFile = (file: File) => {
    onFileSelect(file);
  };

  const handleDragEnter = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);
  }, []);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);

    const files = e.dataTransfer.files;
    if (files.length > 0) {
      handleFile(files[0]);
    }
  }, []);

  //
  //
  //

  return (
    <div className="w-full h-full">
      <div
        className={cn(
          "h-full relative border-2 border-dashed rounded-lg transition-colors flex flex-col justify-center px-4 py-2 border-[#667085]"
        )}
        onDragEnter={handleDragEnter}
        onDragLeave={handleDragLeave}
        onDragOver={handleDragOver}
        onDrop={handleDrop}
      >
        <input
          type="file"
          className="hidden"
          onChange={handleFileChange}
          id="file-input"
        />

        <label
          htmlFor="file-input"
          className="block w-full h-full cursor-pointer"
        >
          <div
            className={cn(
              "flex gap-1 w-full h-full",
              view === "column"
                ? "flex-col justify-center items-center"
                : "flex-row justify-start items-center"
            )}
          >
            <FileUp
              size={32}
              fill={isDragging ? "#7E77F8" : "#667085"}
              color="#fff"
            />
            <p
              className={cn(
                "text-base",
                isDragging ? "text-main" : "text-[#06042B]",
                view === "column" ? "text-center" : "text-left"
              )}
            >
              Upload your resume
            </p>
          </div>
        </label>
      </div>
    </div>
  );
};

export default FileUpload;
