"use client";

import * as React from "react";
import { cn } from "@/lib/utils";
import { getBgColorByScore, getColorByScore } from "@/utils/colors";

interface ProgressProps extends React.HTMLAttributes<HTMLDivElement> {
  value?: number | null;
}

const Progress = React.forwardRef<HTMLDivElement, ProgressProps>(
  ({ className, value, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={cn(
          "relative h-4 w-full overflow-hidden rounded-full bg-[#EBEBEB]",
          className
        )}
        {...props}
      >
        <div
          className={cn(
            "h-full w-full flex-1 transition-all",
            value ? getBgColorByScore(value) : ""
          )}
          style={{
            transform: `translateX(-${100 - (value || 0)}%)`,
            width: "100%",
          }}
        />
      </div>
    );
  }
);

Progress.displayName = "Progress";

interface CircleProgressProps {
  percent: number;
}

const CircleProgress = ({ percent }: CircleProgressProps) => {
  const getStrokeColor = () => {
    if (percent <= 20) return "#EF4444";
    if (percent <= 40) return "#FFB706";
    if (percent <= 60) return "#FFEC18";
    if (percent <= 80) return "#18B953";
    if (percent > 80) return "#15803D";
    return "#E0E2E7";
  };

  return (
    <div className="flex items-center justify-center h-full">
      <div className="relative w-[200px] h-[200px]">
        <svg
          className="absolute top-0 left-0 w-full h-full transform -rotate-90"
          viewBox="0 0 100 100"
        >
          <circle
            cx="50"
            cy="50"
            r="40"
            fill="transparent"
            stroke="#EBEBEB"
            strokeWidth="8"
          />
          <circle
            cx="50"
            cy="50"
            r="40"
            fill="transparent"
            stroke={getStrokeColor()}
            strokeWidth="8"
            strokeDasharray="251.2"
            strokeDashoffset={251.2 * (1 - percent / 100)}
          />
        </svg>
        <div
          className={cn(
            "absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-4xl font-bold",
            getColorByScore(percent)
          )}
        >
          {percent}%
        </div>
      </div>
    </div>
  );
};

export { Progress, CircleProgress };
