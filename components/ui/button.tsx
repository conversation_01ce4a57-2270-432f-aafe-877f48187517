import * as React from "react";
import { Slot } from "@radix-ui/react-slot";
import { cva, type VariantProps } from "class-variance-authority";

import { cn } from "@/lib/utils";

const buttonVariants = cva(
  "inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-[10px] text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",
  {
    variants: {
      variant: {
        primary:
          "bg-primary-500 text-white hover:bg-primary-600 disabled:bg-primary-200",
        danger: "bg-red-300 text-white hover:bg-red-400 disabled:bg-red-50",
        "danger-outline":
          "border border-red-300 text-red-300 hover:bg-red-50 disabled:border-red-200 disabled:text -red-200",
        "danger-ghost": "text-red-300 hover:bg-red-50 disabled:text-red-200",
        outline:
          "border border-primary-500 bg-white text-primary-500 hover:bg-primary-50 disabled:text-primary-400",
        secondary:
          "bg-white text-gray-700 border border-gray-200 hover:border-gray-200 hover:bg-gray-50 disabled:border-gray-100 disabled:text-gray-300",
        ghost: "text-primary-500 hover:bg-primary-50 disabled:text-primary-400",
        link: "text-primary underline-offset-4 hover:underline",
      },
      size: {
        default: "h-10 px-4 py-2",
        sm: "h-9 rounded-md px-3",
        lg: "h-11 rounded-md px-8",
        icon: "h-10 w-10",
      },
    },
    defaultVariants: {
      variant: "primary",
      size: "default",
    },
  }
);

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean;
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, asChild = false, ...props }, ref) => {
    const Comp = asChild ? Slot : "button";
    return (
      <Comp
        className={cn(buttonVariants({ variant, size, className }))}
        ref={ref}
        {...props}
      />
    );
  }
);
Button.displayName = "Button";

export { Button, buttonVariants };
