{"name": "nextjs", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@hookform/resolvers": "^3.9.0", "@lexical/react": "^0.30.0", "@next/swc-wasm-nodejs": "13.5.1", "@next/third-parties": "^15.2.4", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-alert-dialog": "^1.1.1", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-collapsible": "^1.1.0", "@radix-ui/react-context-menu": "^2.2.1", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-hover-card": "^1.1.1", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-menubar": "^1.1.1", "@radix-ui/react-navigation-menu": "^1.2.0", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-radio-group": "^1.2.0", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slider": "^1.2.0", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.2.1", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.8", "@tanstack/react-query": "^5.71.3", "@tanstack/react-query-devtools": "^5.71.3", "@types/node": "20.6.2", "@types/react": "18.2.22", "@types/react-dom": "18.2.7", "assemblyai": "^4.9.0", "autoprefixer": "10.4.15", "axios": "^1.8.4", "bufferutil": "^4.0.9", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.0", "date-fns": "^3.6.0", "diff-match-patch": "^1.0.5", "elevenlabs": "^1.56.0", "embla-carousel-react": "^8.3.0", "eslint": "8.49.0", "eslint-config-next": "13.5.1", "firebase": "^11.6.0", "html-to-pdfmake": "^2.5.24", "htmldiff-js": "^1.0.5", "input-otp": "^1.2.4", "lexical": "^0.30.0", "lucide-react": "^0.446.0", "markdown-it": "^14.1.0", "next": "^14.1.0", "next-intl": "^4.0.2", "next-themes": "^0.3.0", "pdfmake": "^0.2.19", "postcss": "8.4.30", "react": "18.2.0", "react-day-picker": "^8.10.1", "react-device-detect": "^2.2.3", "react-dom": "18.2.0", "react-fast-marquee": "^1.6.5", "react-hook-form": "^7.53.0", "react-pdf": "8.0.2", "react-resizable-panels": "^2.1.3", "react-use": "^17.6.0", "recharts": "^2.12.7", "sharp": "^0.33.5", "sonner": "^1.5.0", "tailwind-merge": "^2.5.2", "tailwindcss": "3.3.3", "tailwindcss-animate": "^1.0.7", "typescript": "5.2.2", "utf-8-validate": "^6.0.5", "vaul": "^0.9.9", "zod": "^3.23.8", "zustand": "^5.0.5"}, "devDependencies": {"@types/diff-match-patch": "^1.0.36", "@types/html-to-pdfmake": "^2.4.4", "@types/pdfmake": "^0.2.11"}}