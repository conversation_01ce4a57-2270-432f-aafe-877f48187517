/**
 * Hàm tính thời gian đã trôi qua từ một thời điểm đến hiện tại
 * @param date Thời điểm trong quá khứ (Date object hoặc timestamp)
 * @param locale Ngôn ngữ hiển thị (mặc định: 'vi-VN')
 * @returns Chuỗi biểu thị thời gian đã trôi qua
 */

const timeUnits = {
  now: "just now",
  seconds: "seconds ago",
  minute: "minute ago",
  minutes: "minutes ago",
  hour: "hour ago",
  hours: "hours ago",
  day: "day ago",
  days: "days ago",
  week: "week ago",
  weeks: "weeks ago",
  month: "month ago",
  months: "months ago",
  year: "year ago",
  years: "years ago",
};

export const timeAgo = (date: Date | number | string): string => {
  // Chuyển đổi tham số date thành đối tượng Date
  const inputDate = date instanceof Date ? date : new Date(date);

  // Thời điểm hiện tại
  const now = new Date();

  // Tính khoảng thời gian đã trôi qua (tính bằng mili giây)
  const diffMs = now.getTime() - inputDate.getTime();

  // Chuyển đổi thành giây
  const diffSeconds = Math.floor(diffMs / 1000);

  // Các ngưỡng thời gian (tính bằng giây)
  const MINUTE = 60;
  const HOUR = MINUTE * 60;
  const DAY = HOUR * 24;
  const WEEK = DAY * 7;
  const MONTH = DAY * 30; // Xấp xỉ
  const YEAR = DAY * 365; // Xấp xỉ

  // Xác định thời gian đã trôi qua
  if (diffSeconds < 5) {
    return timeUnits.now;
  } else if (diffSeconds < MINUTE) {
    return `${diffSeconds} ${timeUnits.seconds}`;
  } else if (diffSeconds < MINUTE * 2) {
    return `1 ${timeUnits.minute}`;
  } else if (diffSeconds < HOUR) {
    return `${Math.floor(diffSeconds / MINUTE)} ${timeUnits.minutes}`;
  } else if (diffSeconds < HOUR * 2) {
    return `1 ${timeUnits.hour}`;
  } else if (diffSeconds < DAY) {
    return `${Math.floor(diffSeconds / HOUR)} ${timeUnits.hours}`;
  } else if (diffSeconds < DAY * 2) {
    return `1 ${timeUnits.day}`;
  } else if (diffSeconds < WEEK) {
    return `${Math.floor(diffSeconds / DAY)} ${timeUnits.days}`;
  } else if (diffSeconds < WEEK * 2) {
    return `1 ${timeUnits.week}`;
  } else if (diffSeconds < MONTH) {
    return `${Math.floor(diffSeconds / WEEK)} ${timeUnits.weeks}`;
  } else if (diffSeconds < MONTH * 2) {
    return `1 ${timeUnits.month}`;
  } else if (diffSeconds < YEAR) {
    return `${Math.floor(diffSeconds / MONTH)} ${timeUnits.months}`;
  } else if (diffSeconds < YEAR * 2) {
    return `1 ${timeUnits.year}`;
  } else {
    return `${Math.floor(diffSeconds / YEAR)} ${timeUnits.years}`;
  }
};
