/** @type {import('next').NextConfig} */
const createNextIntlPlugin = require('next-intl/plugin');

const withNextIntl = createNextIntlPlugin();

const nextConfig = {
  eslint: {
    ignoreDuringBuilds: true,
  },
  images: { unoptimized: true },
  webpack: (config) => {
    config.resolve.alias.canvas = false;
    return config;
  },
  async rewrites() {
    const RESUME_BUILDER_URL = process.env.NEXT_PUBLIC_RESUME_BUILDER_URL || 'http://localhost:8000';

    return [
      {
        source: '/:locale(en|vi)/build-resume',
        destination: `${RESUME_BUILDER_URL}/:locale`,
        basePath: false,
      },
      {
        source: '/:locale(en|vi)/resume-builder',
        destination: `${RESUME_BUILDER_URL}/:locale/resume-builder`,
        basePath: false,
      },
      {
        source: '/:locale(en|vi)/resume-import',
        destination: `${RESUME_BUILDER_URL}/:locale/resume-import`,
        basePath: false,
      },
      {
        source: '/:locale(en|vi)/resume-parser',
        destination: `${RESUME_BUILDER_URL}/:locale/resume-parser`,
        basePath: false,
      },
      // Xử lý assets (CSS, JS, images)
      {
        source: '/resume-builder-static/_next/:path*',
        destination: `${RESUME_BUILDER_URL}/resume-builder-static/_next/:path*`,
      },

      // Bổ sung cấu hình cho WebSocket
      {
        source: '/_next/webpack-hmr',
        destination: `${RESUME_BUILDER_URL}/_next/webpack-hmr`,
        basePath: false
      }
    ];
  },
};

module.exports = withNextIntl(nextConfig);
