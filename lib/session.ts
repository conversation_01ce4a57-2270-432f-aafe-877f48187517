"use server";

import { SESSION_COOKIE_NAME } from "@/constants/session";
import { cookies } from "next/headers";

export async function createSession(uid: string) {
  cookies().set(SESSION_COOKIE_NAME, uid, {
    httpOnly: true,
    secure: process.env.NODE_ENV === "production",
    maxAge: 60 * 60 * 24, // One day
    path: "/",
  });
}

export async function removeSession() {
  cookies().set(SESSION_COOKIE_NAME, "", {
    httpOnly: true,
    secure: process.env.NODE_ENV === "production",
    path: "/",
    expires: new Date(0), // Đặt ngày hết hạn trong quá khứ
    maxAge: 0, // Đặt thời gian tồn tại là 0
  });
}
