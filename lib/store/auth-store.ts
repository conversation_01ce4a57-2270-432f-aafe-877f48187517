import { create } from "zustand";
import {
  User as FirebaseUser,
  signInWithPopup,
  GoogleAuthProvider,
  signOut,
} from "firebase/auth";
import { auth } from "@/lib/firebase";
import { removeSession } from "@/lib/session";
import { ROUTE } from "@/constants/route";

interface AuthState {
  user: FirebaseUser | null;
  token: string | null;
  userProfile: any | null;
  loading: boolean;
  isPending: boolean;

  // Actions
  setUser: (user: FirebaseUser | null) => void;
  setToken: (token: string | null) => void;
  setUserProfile: (profile: any | null) => void;
  setLoading: (loading: boolean) => void;
  setIsPending: (isPending: boolean) => void;

  // Auth methods
  signInWithGoogle: () => Promise<void>;
  logout: (router?: any) => Promise<void>;
}

export const useAuthStore = create<AuthState>()((set, get) => ({
  user: null,
  token: null,
  userProfile: null,
  loading: false,
  isPending: false,

  setUser: (user) => set({ user }),
  setToken: (token) => set({ token }),
  setUserProfile: (profile) => set({ userProfile: profile }),
  setLoading: (loading) => set({ loading }),
  setIsPending: (isPending) => set({ isPending }),

  signInWithGoogle: async () => {
    try {
      set({ loading: true });
      const provider = new GoogleAuthProvider();
      await signInWithPopup(auth, provider);
    } catch (err) {
      console.error("Google sign in error:", err);
      // Ensure session is cleared if login fails
      await removeSession();
    } finally {
      set({ loading: false });
    }
  },

  logout: async (router = null) => {
    try {
      // First clear session on server
      await removeSession();

      // Then logout from auth provider
      await signOut(auth);

      // Finally reset state
      set({
        user: null,
        token: null,
        userProfile: null,
      });

      // Redirect if router is provided
      if (router) {
        router.push(ROUTE.LOGIN);
      }
    } catch (err) {
      console.error("Logout error:", err);
    }
  },
}));
