"use client";
import { ResponseUploadResume, ResponseEvaluate } from "@/apis/resume/types";
import { create } from "zustand";

interface ResumeState {
  header: { fileName: string };
  navigateToScore: { resume: ResponseUploadResume } | null;
  navigateToOptimize: {
    radio: "upload" | "select";
    resume: ResponseUploadResume | null;
    file: File | null;
    jobDescription: string;
    evaluateData: ResponseEvaluate | null;
  } | null;
  callback: (() => void) | null;
  
  // Actions
  onChangeHeader: (header: { fileName: string }) => void;
  onNavigateToScore: (resume: ResponseUploadResume) => void;
  onResetNavigateToScore: () => void;
  onNavigateToOptimize: (data: {
    radio: "upload" | "select";
    resume: ResponseUploadResume | null;
    file: File | null;
    jobDescription: string;
    evaluateData: ResponseEvaluate | null;
  }) => void;
  onResetNavigateToOptimize: () => void;
  onBack: () => void;
  setCallback: (callback: (() => void) | null) => void;
}

export const useResumeStore = create<ResumeState>((set) => ({
  // State
  header: { fileName: "" },
  navigateToScore: null,
  navigateToOptimize: null,
  callback: null,
  evaluateData: null,
  resumeData: null,
  
  // Actions
  onChangeHeader: (header) => set({ header }),
  
  onNavigateToScore: (resume) => set({ 
    navigateToScore: { resume } 
  }),
  
  onResetNavigateToScore: () => set({ 
    navigateToScore: null 
  }),
  
  onNavigateToOptimize: (data) => set({ 
    navigateToOptimize: data 
  }),
  
  onResetNavigateToOptimize: () => set({ 
    navigateToOptimize: null 
  }),

  setCallback: (callback) => set({ callback }),
  
  onBack: () => set((state) => {
    // Execute callback if exists
    if (state.callback) {
      state.callback();
      return { callback: null }; // Clear callback after execution
    }
    return {};
  }),
}));

// Hook sử dụng để tương thích với cách gọi cũ
export const useResume = () => {
  return useResumeStore();
};
