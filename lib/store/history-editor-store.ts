import { create } from "zustand";

// Đ<PERSON>nh nghĩa interface cho editor state
interface HistoryEditorState {
  canUndo: boolean;
  canRedo: boolean;
  isBold: boolean;
  isItalic: boolean;
  isUnderline: boolean;
  fontSize: string;
  fontFamily: string;
  fontColor: string;
  isOrderedList: boolean;
  isUnorderedList: boolean;
}

// Default state
const defaultState: HistoryEditorState = {
  canUndo: false,
  canRedo: false,
  isBold: false,
  isItalic: false,
  isUnderline: false,
  fontSize: "12px",
  fontFamily: "var(--font-inter)",
  fontColor: "#06042B",
  isOrderedList: false,
  isUnorderedList: false,
};

// Tạo store Zustand
interface HistoryEditorStore extends HistoryEditorState {
  setEditorState: (state: Partial<HistoryEditorState>) => void;
  resetEditorState: () => void;
}

export const useHistoryEditorState = create<HistoryEditorStore>((set) => ({
  ...defaultState,
  setEditorState: (state) => set((prevState) => ({ ...prevState, ...state })),
  resetEditorState: () => set(defaultState),
}));
