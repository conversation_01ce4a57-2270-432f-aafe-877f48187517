interface PendoConfig {
  visitor: {
    id: string;
    email?: string;
    role?: string;
    [key: string]: any;
  };
  account: {
    id: string;
    name?: string;
    [key: string]: any;
  };
}

declare global {
  interface Window {
    pendo: {
      initialize: (config: PendoConfig) => void;
      identify: (config: PendoConfig) => void;
    };
  }
}

export const initializePendo = (config: PendoConfig) => {
  if (typeof window !== "undefined" && window.pendo) {
    window.pendo.initialize(config);
  }
};

export const identifyPendo = (config: PendoConfig) => {
  if (typeof window !== "undefined" && window.pendo) {
    window.pendo.identify(config);
  }
};
