export interface InterviewState {
  questionCount: number;
  isFinished: boolean;
  started: boolean;
}

export const INITIAL_PROMPT = `You are an experienced technical interviewer conducting a software engineering interview. Focus on general software engineering questions that assess the candidate's overall understanding and experience. Keep the conversation natural and flowing.

Important guidelines:
1. Ask general questions about software development practices, methodologies, and experiences
2. Focus on topics like: 
   - Software development lifecycle
   - Project management methodologies
   - Team collaboration
   - Problem-solving approaches
   - Best practices and patterns
3. Avoid overly specific technical implementation questions
4. Keep responses conversational and engaging
5. Reference previous answers when asking follow-up questions
6. Maintain a natural flow between questions

Start by introducing yourself briefly and ask your first general question about software engineering.`;

export const FOLLOW_UP_PROMPT = (messages: { role: string; content: string }[]) => {
  const conversationHistory = messages
    .filter(msg => msg.role !== "system")
    .map(msg => msg.content)
    .join("\n");

  return `
Continue the interview based on the previous conversation. Current progress: ${(messages.length - 1) / 2} questions asked.

Conversation history summary:
${conversationHistory}

Guidelines for your next response:
1. If this is question #5:
   - Provide detailed feedback about the candidate's overall performance
   - Highlight their strengths and areas for improvement
   - End the interview professionally
2. Otherwise:
   - Acknowledge their previous answer and relate it to the next question
   - Ask a new general software engineering question
   - Ensure the question flows naturally from the previous discussion

Remember to maintain a conversational tone and keep the discussion focused on general software engineering concepts.`;
};

export async function getAIResponse(messages: { role: string; content: string }[]) {
  try {
    const response = await fetch('https://api.x.ai/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${process.env.NEXT_PUBLIC_XAI_API_KEY}`
      },
      body: JSON.stringify({
        messages: messages,
        model: 'grok-2-latest',
        stream: false,
        temperature: 0.7
      })
    });

    const data = await response.json();
    return data.choices[0].message.content;
  } catch (error) {
    console.error('Error calling X.AI API:', error);
    return 'I apologize, but I encountered an error. Could you please repeat your answer?';
  }
}
