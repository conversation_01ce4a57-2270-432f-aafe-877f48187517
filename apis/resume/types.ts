export interface FeedbackItem {
  feedback: string;
  is_can_auto_apply: boolean;
  original_text?: string;
  suggested_text?: string;
}

interface GeneralCVQualityScores {
  impact: FeedbackItem[];
  brevity: FeedbackItem[];
  style: FeedbackItem[];
  spelling_grammar: FeedbackItem[];
  contact_information: FeedbackItem[];
}
interface JobMatchScores {
  skills_match: FeedbackItem[];
  relevant_keywords: FeedbackItem[];
  experience_alignment: FeedbackItem[];
}
interface SuggestionOptimize {
  general_cv_quality_scores: GeneralCVQualityScores;
  job_match_scores: JobMatchScores;
}

export interface ResponseEvaluate {
  success: boolean;
  detailedEvaluation: {
    general_assessment: {
      scores: {
        impact: {
          score: number; // 0-100
          strengths: string[];
          feedback: string;
          calculation: string;
        };
        brevity: {
          score: number; // 0-100
          strengths: string[];
          feedback: string;
          calculation: string;
        };
        style: {
          score: number; // 0-100
          strengths: string[];
          feedback: string;
          calculation: string;
        };
        spelling_grammar: {
          score: number; // 0-100
          strengths: string[];
          feedback: string;
          calculation: string;
        };
        contact_information: {
          score: number; // 0-100
          strengths: string[];
          feedback: string;
          calculation: string;
        };
      };
      total_score: number; // 0-100
      summary: string;
    };
    job_match_assessment?: {
      scores: {
        skills_match: {
          score: number; // 0-100
          matching_skills: string[];
          missing_skills: string[];
          feedback: string;
          calculation: string;
        };
        relevant_keywords: {
          score: number; // 0-100
          matching_keywords: string[];
          missing_keywords: string[];
          feedback: string;
          calculation: string;
        };
        experience_alignment: {
          score: number; // 0-100
          matching_experience: string[];
          missing_experience: string[];
          feedback: string;
          calculation: string;
        };
      };
      job_requirements: {
        required_skills: string[];
        key_keywords: string[];
        experience_requirements: string[];
      };
      total_score: number; // 0-100
      summary: string;
      tailoring_recommendations: string[];
    };
    combined_total_score: number; // 0-100
    overall_summary: string;
    recommendation_level: {
      level:
        | "Highly Recommended"
        | "Recommended"
        | "Consider with Reservations"
        | "Not Recommended";
      description: string;
      color: string; // hex color code
    };
    key_strengths: Array<{
      criterion: string;
      score: number;
      description: string;
    }>;
    improvement_areas: Array<{
      criterion: string;
      score: number;
      description: string;
      suggestions: string[];
    }>;
    industry_recommendation?: {
      industry: string;
      confidence: string;
      identified_industry_skills: string[];
      recommendations: string[];
    };
    detailed_scores?: any;
  };
  suggestion_optimize: SuggestionOptimize;
  hasJobDescription: boolean;
  filename: string;
  fileSize: string;
  markdown_content: string;
}

export type RequestUploadResume = File;

export interface ResponseUploadResume {
  resume_name: string;
  id: string;
  uid: string;
  file_path: string | File; // File: optimistic update ui
  download_url?: string; // presigned URL từ S3
  created_at: string;
  updated_at: string;
  markdown_content: string;
  isLoading?: boolean; // optional for optimistic update ui
}

export type ResponseGetAllResumes = Array<ResponseUploadResume>;

export type RequestUpdateResume = {
  id: string;
  resume_name: string;
};

export type ResponseUpdateResume = ResponseUploadResume;

export enum ERewriteSuggestion {
  STANDARD = "standard",
  SHORTEN = "shorten",
  FORMAL = "formal",
  LONGER = "longer",
}

export interface RequestRewriteSuggestion {
  text: string;
  style: ERewriteSuggestion;
}

export interface ResponseRewriteSuggestion {
  original_text: string;
  rewritten_text: string;
}
