import { AxiosResponse } from "axios";
import api from "../axios";
import {
  RequestRewriteSuggestion,
  RequestUpdateResume,
  RequestUploadResume,
  ResponseEvaluate,
  ResponseGetAllResumes,
  ResponseRewriteSuggestion,
  ResponseUploadResume,
} from "./types";

export const evaluateCV = async (form: FormData, signal?: AbortSignal) => {
  const response = await api.post<FormData, AxiosResponse<ResponseEvaluate>>(
    "/api/evaluate-cv",
    form,
    {
      headers: { "Content-Type": "multipart/form-data" },
      signal,
    }
  );
  return response.data;
};

export const getAllResumes = async () => {
  const response = await api.get<any, AxiosResponse<ResponseGetAllResumes>>(
    "/api/resumes"
  );
  return response.data;
};

export const uploadResume = async (
  file: RequestUploadResume,
  signal?: AbortSignal
) => {
  const formData = new FormData();
  formData.append("resume_name", file.name);
  formData.append("file", file);

  const response = await api.post<
    FormData,
    AxiosResponse<ResponseUploadResume>
  >("/api/resumes", formData, {
    headers: { "Content-Type": "multipart/form-data" },
    signal,
  });
  return response.data;
};

export const updateResume = async (request: RequestUpdateResume) => {
  const response = await api.put<
    Pick<RequestUpdateResume, "resume_name">,
    AxiosResponse<ResponseUploadResume>
  >(`/api/resumes/${request.id}`, { resume_name: request.resume_name });
  return response.data;
};

export const deleteResume = async (id: string) => {
  const response = await api.delete(`/api/resumes/${id}`);
  return response.data;
};

export const rewriteSuggestion = async (request: RequestRewriteSuggestion) => {
  const response = await api.post<
    RequestRewriteSuggestion,
    AxiosResponse<ResponseRewriteSuggestion>
  >("/api/rewrite", request);
  return response.data;
};
