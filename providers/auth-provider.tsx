"use client";

import { useEffect } from "react";
import { onAuthStateChanged } from "firebase/auth";
import { auth } from "@/lib/firebase";
import { createSession, removeSession } from "@/lib/session";
import { login } from "@/apis/auth";
import { useAuthStore } from "@/lib/store/auth-store";

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const {
    setUser,
    setToken,
    setUserProfile,
    setLoading,
    setIsPending,
    logout,
  } = useAuthStore();

  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, async (authUser) => {
      setLoading(true);
      try {
        if (authUser) {
          // User is signed in
          setUser(authUser);
          const idToken = await authUser.getIdToken();
          setToken(idToken);

          // Call backend to verify token and get user data
          setIsPending(true);
          try {
            const response = await login(idToken);
            setUserProfile(response);
            // Save session
            await createSession(idToken);
          } catch (error) {
            console.error("Error verifying token with backend:", error);
            // If API login fails, ensure session is cleared and user is logged out
            await logout();
          } finally {
            setIsPending(false);
          }
        } else {
          // User is signed out
          setUser(null);
          setToken(null);
          setUserProfile(null);
          // Ensure session is also cleared when user is detected as logged out
          await removeSession();
        }
      } catch (err) {
        console.error("Auth state change error:", err);
      } finally {
        setLoading(false);
      }
    });

    return () => unsubscribe();
  }, []);

  return <>{children}</>;
}

export { useAuthStore as useAuth };
