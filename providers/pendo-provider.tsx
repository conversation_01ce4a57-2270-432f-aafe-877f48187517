"use client";

import { createContext, useContext, useEffect } from "react";
import { initializePendo, identifyPendo } from "@/lib/pendo";
import { useAuth } from "./auth-provider";

interface PendoContextType {
  identify: (visitorId: string, accountId: string, metadata?: any) => void;
}

const PendoContext = createContext<PendoContextType | undefined>(undefined);

export const PendoProvider = ({ children }: { children: React.ReactNode }) => {
  const { user, userProfile } = useAuth();

  useEffect(() => {
    // Initialize Pendo with default configuration
    initializePendo({
      visitor: {
        id: "default-visitor",
      },
      account: {
        id: "default-account",
      },
    });
  }, []);

  // Effect to update Pendo when user changes
  useEffect(() => {
    if (user) {
      identifyPendo({
        visitor: {
          id: user.uid,
          email: user.email || undefined,
          name: user.displayName || undefined,
          photoURL: user.photoURL || undefined,
          // Add any additional user properties from userProfile
          ...(userProfile || {}),
        },
        account: {
          id: userProfile?.email || "default-account",
          name: userProfile?.organizationName,
          // Add any other organization properties from userProfile
          ...(userProfile?.organization || {}),
        },
      });
    }
  }, [user, userProfile]);

  const identify = (visitorId: string, accountId: string, metadata?: any) => {
    identifyPendo({
      visitor: {
        id: visitorId,
        ...metadata?.visitor,
      },
      account: {
        id: accountId,
        ...metadata?.account,
      },
    });
  };

  return (
    <PendoContext.Provider value={{ identify }}>
      {children}
    </PendoContext.Provider>
  );
};

export const usePendo = () => {
  const context = useContext(PendoContext);
  if (context === undefined) {
    throw new Error("usePendo must be used within a PendoProvider");
  }
  return context;
};
