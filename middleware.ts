import { NextResponse } from "next/server";
import type { NextRequest } from "next/server";
import createMiddleware from "next-intl/middleware";
import { routing } from "./i18n/routing";

import { SESSION_COOKIE_NAME } from "./constants/session";
import { ROUTE, ROUTE_ARRAY } from "./constants/route";

const APP_URL =
  process.env.NODE_ENV === "production"
    ? process.env.NEXT_PUBLIC_APP_URL || "http://localhost:3000"
    : "http://localhost:3000";

export const i18nMiddleware = createMiddleware(routing);

export async function middleware(request: NextRequest) {
  const { pathname, searchParams } = request.nextUrl;

  // Skip middleware for API routes and static files
  if (
    pathname.startsWith("/api") ||
    pathname.startsWith("/_next") ||
    pathname.includes(".")
  ) {
    return NextResponse.next();
  }

  // Handle locale extraction more safely
  const pathParts = pathname.split("/").filter(Boolean);
  const locale =
    pathParts[0] && ["en", "vi"].includes(pathParts[0]) ? pathParts[0] : "en";
  const pathWithoutLocale =
    "/" +
    (pathParts[0] && ["en", "vi"].includes(pathParts[0])
      ? pathParts.slice(1).join("/")
      : pathParts.join("/"));

  // Get session from cookies
  const session = request.cookies.get(SESSION_COOKIE_NAME)?.value || "";

  // Process auth routes
  const authRoutes = [ROUTE.LOGIN, ROUTE.SIGNUP, ROUTE.RESET_PASSWORD];
  const isAuthPage = authRoutes.some((route) => pathWithoutLocale === route);

  // Check if the current path is valid
  const isValidPath = ROUTE_ARRAY.includes(pathWithoutLocale);

  if (session) {
    // Check if URL contains redirect parameter
    const redirectParam = searchParams.get("redirect");
    if (redirectParam) {
      // Redirect directly to the specified path with locale
      return NextResponse.redirect(
        new URL(`/${locale}${redirectParam}`, APP_URL)
      );
    }

    // Case 1: Has session -> redirect to home if on auth page
    if (isAuthPage) {
      const homeUrl = new URL(`/${locale}${ROUTE.RESUME_MANAGEMENT}`, APP_URL);
      return NextResponse.redirect(homeUrl);
    }

    // Case 1.1: Has session but invalid path -> redirect to home
    if (!isValidPath) {
      const homeUrl = new URL(`/${locale}${ROUTE.RESUME_MANAGEMENT}`, APP_URL);
      return NextResponse.redirect(homeUrl);
    }
  } else {
    // Case 2: No session -> redirect to login unless already on auth page
    if (!isAuthPage && !pathname.startsWith("/public")) {
      const loginUrl = new URL(`/${locale}${ROUTE.LOGIN}`, APP_URL);
      // Save the current path for redirect after login
      loginUrl.searchParams.set("redirect", pathWithoutLocale);
      return NextResponse.redirect(loginUrl);
    }
  }

  // Apply i18n middleware
  const response = i18nMiddleware(request);
  return response;
}

export const config = {
  matcher: [
    // Match all paths except static files and API routes
    "/((?!api|_next/static|_next/image|favicon.ico|.*\\.png$).*)",
  ],
};
