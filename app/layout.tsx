import { Metadata } from "next";
import { ReactNode } from "react";

export const metadata: Metadata = {
  metadataBase: new URL("https://easyhire.global"),
  title: "AI Mate for Job Seekers - From Resume to Offer",
  description:
    "Get AI-powered help to build, score and optimize your resume, ace interviews, negotiate offers, and land your dream job.",

  icons: {
    icon: [
      { url: "https://easyhire.global/favicon/logo.svg" },
      {
        url: "https://easyhire.global/favicon/logo.svg",
        sizes: "250x150",
        type: "image/svg",
      },
      {
        url: "https://easyhire.global/favicon/logo.svg",
        sizes: "500x300",
        type: "image/svg",
      },
    ],
    apple: [
      {
        url: "https://easyhire.global/favicon/logo.svg",
        sizes: "250x150",
        type: "image/svg",
      },
    ],
  },

  openGraph: {
    title: "AI Mate for Job Seekers - From Resume to Offer",
    description:
      "Get AI-powered help to build, score and optimize your resume, ace interviews, negotiate offers, and land your dream job.",
    url: "https://easyhire.global",
    type: "website",
    siteName: "EasyHire",
    images: [
      {
        url: "https://easyhire.global/thumbnails/1200x630.png",
        alt: "1200x630",
        width: 1200,
        height: 630,
      },
      {
        url: "https://easyhire.global/thumbnails/600x315.png",
        alt: "600x315",
        width: 600,
        height: 315,
      },
    ],
  },

  twitter: {
    card: "summary_large_image",
    title: "AI Mate for Job Seekers - From Resume to Offer",
    description:
      "Get AI-powered help to build, score and optimize your resume, ace interviews, negotiate offers, and land your dream job.",
    images: [
      {
        url: "https://easyhire.global/thumbnails/1200x675.png",
        alt: "1200x675",
        width: 1200,
        height: 675,
      },
      {
        url: "https://easyhire.global/thumbnails/800x418.png",
        alt: "800x418",
        width: 800,
        height: 418,
      },
    ],
  },

  alternates: {
    canonical: "https://easyhire.global",
    languages: {
      en: "https://easyhire.global/en",
      vi: "https://easyhire.global/vi",
    },
  },

  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
};

type Props = {
  children: ReactNode;
};

// Since we have a `not-found.tsx` page on the root, a layout file
// is required, even if it's just passing children through.
export default function RootLayout({ children }: Props) {
  return children;
}
