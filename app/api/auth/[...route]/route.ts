import { NextRequest, NextResponse } from 'next/server';
import axios from 'axios';

// You'll need to set your backend URL in .env.local
const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';

export async function POST(
  request: NextRequest,
  { params }: { params: { route: string[] } }
) {
  try {
    const route = params.route.join('/');
    const body = await request.json();
    
    // Forward the request to the backend API
    const response = await axios.post(`${API_URL}/auth/${route}`, body);
    
    return NextResponse.json(response.data);
  } catch (error: any) {
    console.error(`Error in auth/${params.route.join('/')}:`, error);
    
    return NextResponse.json(
      { 
        error: error.response?.data?.detail || 'An error occurred with the authentication service' 
      },
      { status: error.response?.status || 500 }
    );
  }
}
