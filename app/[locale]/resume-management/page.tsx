"use client";

import React, { useState } from "react";
import { Rows3, Grid2x2 } from "lucide-react";
import { cn } from "@/lib/utils";
import ViewResume, {
  useViewResume,
} from "@/components/common/resume-management/view/view";

export type ViewType = "row" | "column";

const ResumeManagement = () => {
  const { view, setView } = useViewResume();
  return (
    <div className="px-4 py-5 flex flex-col gap-3 h-full">
      <div className="flex justify-between items-center">
        <p className="text-xl font-semibold text-[#06042B]">My Resume</p>

        <div className="flex items-center gap-0.5 border-2 border-[#E7E7E7] bg-[#E7E7E7] rounded-lg w-fit overflow-hidden">
          <div
            className={cn(
              "p-2",
              view === "row" ? "bg-background" : "bg-[#F1F1F1]"
            )}
          >
            <Rows3
              className={cn(
                "cursor-pointer",
                view === "row" ? "text-main" : "text-[#667085]"
              )}
              onClick={() => setView("row")}
            />
          </div>
          <div
            className={cn(
              "p-2 cursor-pointer",
              view === "column" ? "bg-background" : "bg-[#F1F1F1]"
            )}
          >
            <Grid2x2
              className={cn(view === "column" ? "text-main" : "text-[#667085]")}
              onClick={() => setView("column")}
            />
          </div>
        </div>
      </div>

      <ViewResume.All />
    </div>
  );
};

export default ResumeManagement;
