"use client";

import { Form<PERSON>rovider } from "react-hook-form";
import React, { useCallback, useEffect, useRef, useState } from "react";
import { useForm } from "react-hook-form";
import { useMutation } from "@tanstack/react-query";

import FormOptimizeResume, {
  FormOptimizeResumeType,
} from "@/components/common/ai-optimize-resume/form-optimize-resume";
import { CircleChevronLeft } from "lucide-react";
import { cn } from "@/lib/utils";
import OptimizeResume from "@/components/common/ai-optimize-resume/optimize-resume";
import {
  evaluateCV,
} from "@/apis/resume/resume";
import { ResponseEvaluate, ResponseUploadResume } from "@/apis/resume/types";
import { LoadingOverlay } from "@/components/common/loading";
import { MESSAGES_LOADING_EVALUATE_RESUME } from "@/constants/loading-message";
import { useResume } from "@/lib/store/resume-store";

enum OptimizeResumeStep {
  UPLOAD = "UPLOAD",
  OPTIMIZE = "OPTIMIZE",
}

const AIOptimizeResume: React.FC = () => {
  const {
    navigateToOptimize,
    onResetNavigateToOptimize,
    onChangeHeader,
    setCallback,
    onBack,
    header,
  } = useResume();
  const [isLoading, setIsLoading] = useState(false);
  const cancelTokenRef = useRef<AbortController | null>(null);

  const [step, setStep] = useState<OptimizeResumeStep>(
    OptimizeResumeStep.UPLOAD
  );

  const methods = useForm<FormOptimizeResumeType>({
    defaultValues: {
      radio: "select",
      file: null,
      selectFile: null,
      jobDescription: "",
      evaluateData: null,
    },
  });
  const {
    watch,
    getValues,
    reset,
    setValue
  } = methods;

  const callback = useCallback(() => {
    setStep(OptimizeResumeStep.UPLOAD);
    setValue("evaluateData", null);
    onChangeHeader({ fileName: "" });
  }, []);

  const onUpdateHeader = () => {
    const { file, selectFile, radio } = getValues();
    const fileName = radio === "select" ? selectFile?.resume_name : file?.name;
    if (fileName) onChangeHeader({ fileName });
  };

  const { mutateAsync: mutateEvaluateCV } = useMutation({
    mutationKey: ["ai-resume-score"],
    mutationFn: (formData: FormData) => {
      cancelTokenRef.current = new AbortController();
      return evaluateCV(formData, cancelTokenRef.current.signal);
    },
    onMutate: () => {
      setIsLoading(true);
    },
    onSuccess: (data) => {
      setValue("evaluateData", data);
      setStep(OptimizeResumeStep.OPTIMIZE);
      onUpdateHeader();
      setCallback(callback);
    },
    onSettled: () => {
      setIsLoading(false);
    },
  });

  const onNextStep = async () => {
    const { file, radio, selectFile, jobDescription, evaluateData } = getValues();

    //* handle case when user navigate from rescore to optimize resume page
    if (selectFile && radio === "select" && !evaluateData) {
      const formData = new FormData();
      formData.append("resume_id", selectFile.id);
      if (jobDescription) formData.append("jobDescription", jobDescription);

      await mutateEvaluateCV(formData);
      return;
    }

    //* handle case when user directly optimize resume
    if (radio === "upload" && file && !evaluateData) {
      const formData = new FormData();
      formData.append("file", file);
      if (jobDescription) formData.append("jobDescription", jobDescription);
      await mutateEvaluateCV(formData);
      return;
    }
  };

  const handleCancelUpload = () => {
    if (cancelTokenRef.current) {
      cancelTokenRef.current.abort();
      setIsLoading(false);
    }
  };

  const renderStep = () => {
    if (step === OptimizeResumeStep.UPLOAD)
      return <FormOptimizeResume onNextStep={onNextStep} />;
    if (step === OptimizeResumeStep.OPTIMIZE)
      return (
        <OptimizeResume
          filename={header.fileName || ""}
          markdown={watch("evaluateData")?.markdown_content || ""}
          evaluateData={watch("evaluateData") as ResponseEvaluate}
        />
      );
  };

  useEffect(() => {
    if (navigateToOptimize) {
      const { resume, file, jobDescription, radio, evaluateData } = navigateToOptimize;
      const form = {
        ...getValues(),
        jobDescription,
        radio,
        evaluateData,
      }
      if (radio === "select") {
        form.selectFile = resume;
      } else {
        form.file = file;
      }
      reset(form);
      if (evaluateData) {
        setStep(OptimizeResumeStep.OPTIMIZE);
        const fileName = radio === "select" ? resume?.resume_name : file?.name;
        onChangeHeader({ fileName: fileName || "" });
        setCallback(callback);
      }
      onResetNavigateToOptimize();
    }
  }, []);

  useEffect(() => {
    return () => {
      onResetNavigateToOptimize();
      onChangeHeader({ fileName: "" });
      setCallback(null);
    };
  }, []);

  return (
    <FormProvider {...methods}>
      {isLoading && (
        <LoadingOverlay
          messages={MESSAGES_LOADING_EVALUATE_RESUME}
          onCancel={handleCancelUpload}
        />
      )}
      <div
        className={cn(
          "flex-col gap-2 px-6 py-2 min-[1024px]:!hidden",
          step === OptimizeResumeStep.OPTIMIZE ? "flex" : "hidden"
        )}
      >
        <div
          className="flex gap-1 items-center cursor-pointer w-fit"
          onClick={onBack}
        >
          <CircleChevronLeft fill="#667085" color="#fff" />
          <p className="text-base text-[#667085]">Back to scan</p>
        </div>
        <h4 className="text-xl font-semibold text-[#06042B]">
          {header.fileName}
        </h4>
      </div>
      {renderStep()}
    </FormProvider>
  );
};

export default AIOptimizeResume;
