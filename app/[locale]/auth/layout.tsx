"use client";

import { useSearchParams } from "next/navigation";
import { useAuth } from "@/providers/auth-provider";
import { ROUTE, ROUTE_ARRAY } from "@/constants/route";
import { useRouter } from "@/i18n/navigation";

export default function Layout({ children }: { children: React.ReactNode }) {
  const { user, loading } = useAuth();

  const router = useRouter();
  const searchParams = useSearchParams();
  const redirectParam = searchParams.get("redirect");

  if (user && !loading) {
    const route = redirectParam
      ? ROUTE_ARRAY.includes(redirectParam)
        ? redirectParam
        : ROUTE.HOME
      : ROUTE.HOME;

    router.push({ pathname: route });
  }

  return children;
}
