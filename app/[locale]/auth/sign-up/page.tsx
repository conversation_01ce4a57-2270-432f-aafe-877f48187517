"use client";

import React from "react";
import Image from "next/image";
import { inter, roboto } from "@/app/fonts";
import { But<PERSON> } from "@/components/ui/button";
import { Input, PasswordInput } from "@/components/ui/input";
import { cn } from "@/lib/utils";
import { Controller, useForm } from "react-hook-form";
import { Link } from "@/i18n/navigation";
import { useAuth } from "@/providers/auth-provider";
import { useSearchParams } from "next/navigation";
import { Loader2 } from "lucide-react";
import { Checkbox } from "@/components/ui/checkbox";
import { ROUTE } from "@/constants/route";
import AuthLayout from "@/components/common/layouts/AuthLayout";

const SOCIALS = [
  // { image: "/images/linkedin.svg", name: "Linked" },
  // { image: "/images/facebook.svg", name: "Facebook" },
  { image: "/images/google.svg", name: "Google" },
];

interface SignUpForm {
  username: string;
  email: string;
  password: string;
  confirmPassword: string;
  isAgreeTerms: boolean;
  isAgreeSendUpdate: boolean;
}

const SignUp = () => {
  const { loading, signInWithGoogle } = useAuth();

  const searchParams = useSearchParams();
  const redirectParam = searchParams.get("redirect");

  const { control, handleSubmit } = useForm<SignUpForm>({
    defaultValues: {
      username: "",
      email: "",
      password: "",
      confirmPassword: "",
      isAgreeTerms: false,
      isAgreeSendUpdate: true,
    },
  });

  const onSubmit = handleSubmit((data) => ({}));

  //
  //
  //

  return (
    <AuthLayout>
      <div className="flex flex-col justify-center items-center gap-6 w-full sm:w-fit">
        <div className="flex items-center justify-between w-full">
          <Link href={ROUTE.HOME}>
            <p
              className={`text-main font-bold text-[28px] cursor-pointer ${inter.className}`}
            >
              EasyHire
            </p>
          </Link>

          <div className="flex justify-end items-center">
            <p className="text-sm text-center text-[#333333]">
              Already have an account?
            </p>
            &nbsp;
            <Link
              href={`${ROUTE.LOGIN}${
                redirectParam ? `?redirect=${redirectParam}` : ""
              }`}
            >
              <p className="text-sm text-center text-main font-bold">Login</p>
            </Link>
          </div>
        </div>
        <div className="bg-background shadow-[0px_8px_16px_0px_#********] p-8 rounded-xl w-full sm:w-fit">
          <div className="flex flex-col gap-9 items-center justify-center">
            <p className="text-xl text-[#333333]">Create Your Account</p>

            <div className="flex flex-col sm:flex-row justify-center items-center gap-5 w-full sm:min-w-[500px]">
              {SOCIALS.map((social) => (
                <Button
                  key={social.name}
                  variant="secondary"
                  className="w-full sm:w-[135px] rounded-lg"
                  disabled={social.name !== "Google" ? true : loading}
                  onClick={
                    social.name === "Google" ? signInWithGoogle : undefined
                  }
                >
                  {social.name === "Google" && loading ? (
                    <Loader2 size={16} className="animate-spin" />
                  ) : null}
                  <Image alt="" width={24} height={24} src={social.image} />
                  <p className="text-sm text-[#333333]">{social.name}</p>
                </Button>
              ))}
            </div>
          </div>

          <p className="text-sm text-center text-[#333333] py-6">Or</p>

          <form className="flex flex-col gap-5" onSubmit={onSubmit}>
            <Controller
              name="username"
              control={control}
              render={({ field }) => (
                <Input
                  {...field}
                  className="w-full"
                  placeholder="Your name*"
                  disabled
                />
              )}
            />
            <Controller
              name="email"
              control={control}
              render={({ field }) => (
                <Input
                  {...field}
                  className="w-full"
                  placeholder="Email*"
                  disabled
                />
              )}
            />
            <Controller
              name="password"
              control={control}
              render={({ field }) => (
                <PasswordInput
                  {...field}
                  className="w-full"
                  placeholder="Password"
                  disabled
                />
              )}
            />

            <Controller
              name="confirmPassword"
              control={control}
              render={({ field }) => (
                <PasswordInput
                  {...field}
                  className="w-full"
                  placeholder="Confirm Password*"
                  disabled
                />
              )}
            />

            <Controller
              name="isAgreeTerms"
              control={control}
              render={({ field: { value, onChange } }) => (
                <div className="flex items-center space-x-2">
                  <Checkbox
                    checked={value}
                    onCheckedChange={onChange}
                    id="terms"
                  />
                  <label
                    htmlFor="terms"
                    className="text-sm text-[#666666] leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                  >
                    I agree to 
                    <strong className="text-main">Terms of Service</strong> and 
                    <strong className="text-main">Privacy policy</strong>
                  </label>
                </div>
              )}
            />

            <Controller
              name="isAgreeSendUpdate"
              control={control}
              render={({ field: { value, onChange } }) => (
                <div className="flex items-center space-x-2">
                  <Checkbox
                    checked={value}
                    onCheckedChange={onChange}
                    id="terms"
                  />
                  <label
                    htmlFor="terms"
                    className="text-sm text-[#666666] leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                  >
                    Send me the new updates
                  </label>
                </div>
              )}
            />

            <Button
              className={cn("text-base font-semibold w-full", roboto.className)}
              disabled
            >
              Create an account
            </Button>
          </form>
        </div>
      </div>
    </AuthLayout>
  );
};

export default SignUp;
