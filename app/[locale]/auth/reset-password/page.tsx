"use client";

import { inter, roboto } from "@/app/fonts";
import AuthLayout from "@/components/common/layouts/AuthLayout";
import { Button } from "@/components/ui/button";
import { Input, PasswordInput } from "@/components/ui/input";
import { ROUTE } from "@/constants/route";
import { Link } from "@/i18n/navigation";
import { cn } from "@/lib/utils";
import { useSearchParams } from "next/navigation";
import React, { useMemo, useState } from "react";
import { Controller, useForm } from "react-hook-form";

const ResetPassword = () => {
  const searchParams = useSearchParams();
  const redirectParam = searchParams.get("redirect");

  const [step, setStep] = useState<1 | 2>(1);

  const renderContent = useMemo(() => {
    if (step === 1) {
      return <EnterEmail onNext={() => setStep(2)} />;
    }

    return <NewPassword />;
  }, [step]);

  return (
    <AuthLayout>
      <div className="flex flex-col justify-center items-center gap-6 w-full sm:w-fit">
        <div className="flex items-center justify-between w-full">
          <Link href={ROUTE.HOME}>
            <p
              className={`text-main font-bold text-[28px] cursor-pointer ${inter.className}`}
            >
              EasyHire
            </p>
          </Link>

          <div className="flex justify-end items-center">
            <p className="text-sm text-center text-[#333333]">
              Don’t have an account?
            </p>
            &nbsp;
            <Link
              href={`${ROUTE.SIGNUP}${
                redirectParam ? `?redirect=${redirectParam}` : ""
              }`}
            >
              <p className="text-sm text-center text-main font-bold">Sign up</p>
            </Link>
          </div>
        </div>

        {renderContent}
      </div>
    </AuthLayout>
  );
};

export default ResetPassword;

const EnterEmail = ({ onNext }: { onNext: VoidFunction }) => {
  const searchParams = useSearchParams();
  const redirectParam = searchParams.get("redirect");

  const { control, handleSubmit, watch } = useForm<{ email: string }>({
    defaultValues: { email: "" },
  });

  const onSubmit = handleSubmit((data) => ({}));

  return (
    <div className="bg-background shadow-[0px_8px_16px_0px_#********] p-8 rounded-xl sm:w-[516px] w-full">
      <div className="flex flex-col gap-6 items-center justify-center">
        <p className="text-xl text-[#333333]">Reset your password</p>

        <form className="flex flex-col gap-5 w-full" onSubmit={onSubmit}>
          <Controller
            name="email"
            control={control}
            render={({ field }) => (
              <Input {...field} className="w-full" placeholder="Email" />
            )}
          />

          <Button
            className={cn("text-base font-semibold w-full", roboto.className)}
            disabled={!watch("email").trim()}
            onClick={onNext}
          >
            Reset my password
          </Button>
        </form>
      </div>

      <Link
        href={`${ROUTE.LOGIN}${
          redirectParam ? `?redirect=${redirectParam}` : ""
        }`}
      >
        <p className="text-sm text-center text-main pt-5">Back to Login</p>
      </Link>
    </div>
  );
};

const NewPassword = () => {
  const searchParams = useSearchParams();
  const redirectParam = searchParams.get("redirect");

  const { control, handleSubmit } = useForm<{
    password: string;
    confirmPassword: string;
  }>({
    defaultValues: { password: "", confirmPassword: "" },
  });

  const onSubmit = handleSubmit((data) => ({}));

  return (
    <div className="bg-background shadow-[0px_8px_16px_0px_#********] p-8 rounded-xl sm:w-[516px] w-full">
      <div className="flex flex-col gap-6 items-center justify-center">
        <p className="text-xl text-[#333333]">Login to your account</p>

        <form className="flex flex-col gap-5 w-full" onSubmit={onSubmit}>
          <Controller
            name="password"
            control={control}
            render={({ field }) => (
              <PasswordInput
                {...field}
                className="w-full"
                placeholder="Password"
                disabled
              />
            )}
          />

          <Controller
            name="confirmPassword"
            control={control}
            render={({ field }) => (
              <PasswordInput
                {...field}
                className="w-full"
                placeholder="Confirm New Password"
                disabled
              />
            )}
          />

          <Button
            className={cn("text-base font-semibold w-full", roboto.className)}
            disabled
          >
            Confirm Change
          </Button>
        </form>
      </div>

      <Link
        href={`${ROUTE.LOGIN}${
          redirectParam ? `?redirect=${redirectParam}` : ""
        }`}
      >
        <p className="text-sm text-center text-main pt-5">Back to Login</p>
      </Link>
    </div>
  );
};
