"use client";

import React from "react";
import Image from "next/image";
import { inter, roboto } from "@/app/fonts";
import { Button } from "@/components/ui/button";
import { Input, PasswordInput } from "@/components/ui/input";
import { cn } from "@/lib/utils";
import { Controller, useForm } from "react-hook-form";
import { Link } from "@/i18n/navigation";
import { useAuth } from "@/providers/auth-provider";
import { useSearchParams } from "next/navigation";
import { Loader2 } from "lucide-react";
import { ROUTE } from "@/constants/route";
import AuthLayout from "@/components/common/layouts/AuthLayout";

const SOCIALS = [
  // { image: "/images/linkedin.svg", name: "Linked" },
  // { image: "/images/facebook.svg", name: "Facebook" },
  { image: "/images/google.svg", name: "Google" },
];

interface LoginForm {
  email: string;
  password: string;
}

const Login = () => {
  const { loading, signInWithGoogle } = useAuth();

  const searchParams = useSearchParams();
  const redirectParam = searchParams.get("redirect");

  const { control, handleSubmit } = useForm<LoginForm>({
    defaultValues: { email: "", password: "" },
  });

  const onSubmit = handleSubmit((data) => ({}));

  //
  //
  //

  return (
    <AuthLayout>
      <div className="flex flex-col justify-center items-center gap-6 w-full sm:w-fit">
        <div className="flex items-center justify-between w-full">
          <Link href={ROUTE.HOME}>
            <p
              className={`text-main font-bold text-[28px] cursor-pointer ${inter.className}`}
            >
              EasyHire
            </p>
          </Link>

          <div className="flex justify-end items-center">
            <p className="text-sm text-center text-[#333333]">
              Don’t have an account?
            </p>
            &nbsp;
            <Link
              href={`${ROUTE.SIGNUP}${
                redirectParam ? `?redirect=${redirectParam}` : ""
              }`}
            >
              <p className="text-sm text-center text-main font-bold">Sign up</p>
            </Link>
          </div>
        </div>
        <div className="bg-background shadow-[0px_8px_16px_0px_#********] p-8 rounded-xl w-full sm:w-fit">
          <div className="flex flex-col gap-9 items-center justify-center">
            <p className="text-xl text-[#333333]">Login to your account</p>

            <div className="flex flex-col sm:flex-row justify-center items-center gap-5 w-full sm:min-w-[500px]">
              {SOCIALS.map((social) => (
                <Button
                  key={social.name}
                  variant="secondary"
                  className="w-full sm:w-[150px] rounded-lg"
                  disabled={social.name !== "Google" ? true : loading}
                  onClick={
                    social.name === "Google" ? signInWithGoogle : undefined
                  }
                >
                  {social.name === "Google" && loading ? (
                    <Loader2 size={16} className="animate-spin" />
                  ) : null}
                  <Image alt="" width={24} height={24} src={social.image} />
                  <p className="text-sm text-[#333333]">{social.name}</p>
                </Button>
              ))}
            </div>
          </div>

          <p className="text-sm text-center text-[#333333] py-6">Or</p>

          <form className="flex flex-col gap-5" onSubmit={onSubmit}>
            <Controller
              name="email"
              control={control}
              render={({ field }) => (
                <Input
                  {...field}
                  className="w-full"
                  placeholder="Email"
                  disabled
                />
              )}
            />
            <Controller
              name="password"
              control={control}
              render={({ field }) => (
                <PasswordInput
                  {...field}
                  className="w-full"
                  placeholder="Password"
                  disabled
                />
              )}
            />
            <Button
              className={cn("text-base font-semibold w-full", roboto.className)}
              disabled
            >
              Login
            </Button>
          </form>

          <Link
            href={`${ROUTE.RESET_PASSWORD}${
              redirectParam ? `?redirect=${redirectParam}` : ""
            }`}
          >
            <p className="text-sm text-center text-main pt-5">
              Forgot password?
            </p>
          </Link>
        </div>
      </div>
    </AuthLayout>
  );
};

export default Login;
