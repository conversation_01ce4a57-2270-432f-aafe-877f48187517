"use client";

import React, { useEffect, useRef, useState } from "react";
import { evaluateCV } from "@/apis/resume/resume";
import FileUpload from "@/components/common/file-upload";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { Controller, useForm } from "react-hook-form";
import { AlertTriangle, CircleCheck, CircleChevronLeft } from "lucide-react";
import { CircleProgress, Progress } from "@/components/ui/progress";
import { cn } from "@/lib/utils";
import { SFPro } from "@/app/fonts";
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import FeedbackIcon from "/public/images/dashboard/ai-resume-score/feedback.svg";
import StrengthsIcon from "/public/images/dashboard/ai-resume-score/strengths.svg";
import MatchingIcon from "/public/images/dashboard/ai-resume-score/matching.svg";
import MissingIcon from "/public/images/dashboard/ai-resume-score/missing.svg";
import Image from "next/image";
import { Badge } from "@/components/ui/badge";
import { snakeToTitleCase } from "@/utils/text";
import { ResponseEvaluate, ResponseUpdateResume } from "@/apis/resume/types";
import { MESSAGES_LOADING_EVALUATE_RESUME } from "@/constants/loading-message";
import { LoadingOverlay } from "@/components/common/loading";
import ComboboxResume from "@/components/common/combobox-resume";
import { getColorByScore } from "@/utils/colors";
import { useRouter } from "@/i18n/navigation";
import { ROUTE } from "@/constants/route";
import { useResume } from "@/lib/store/resume-store";

const RADIO_BUTTON = [
  { value: "select", label: "Select resume from library" },
  { value: "upload", label: "Upload resume" },
];

type RadioButton = "upload" | "select";

type FormValues = {
  radio: RadioButton;
  file: File | null;
  description: string;
  selectFile: ResponseUpdateResume | null;
};

const extractScoreString = (description: string) => {
  const match = description.match(/\(.*?(\d+\/\d+)\)/);
  return match ? match[1] : null;
};

const AIResumeScore: React.FC = () => {
  const route = useRouter();
  const {
    onNavigateToOptimize,
    onResetNavigateToScore,
    navigateToScore,
    header,
    onChangeHeader,
    setCallback,
    onBack,
  } = useResume();
  const queryClient = useQueryClient();
  const cancelTokenRef = useRef<AbortController | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [evaluate, setEvaluate] = useState<ResponseEvaluate | null>(null);

  const { mutateAsync, isSuccess } = useMutation({
    mutationKey: ["ai-resume-score"],
    mutationFn: (formData: FormData) => {
      cancelTokenRef.current = new AbortController();
      return evaluateCV(formData, cancelTokenRef.current.signal);
    },
    onMutate: () => {
      setIsLoading(true);
    },
    onSuccess: (response) => {
      const { file, selectFile, radio } = getValues();
      const fileName =
        radio === "select" ? selectFile?.resume_name : file?.name;
      if (fileName) onChangeHeader({ fileName });
      setEvaluate(response);

      setCallback(() => {
        onChangeHeader({ fileName: "" });
        setEvaluate(null);
        queryClient.resetQueries({ queryKey: ["ai-resume-score"] });
      });
    },
    onSettled: () => {
      setIsLoading(false);
    },
  });

  const { control, handleSubmit, watch, reset, getValues } =
    useForm<FormValues>({
      defaultValues: {
        radio: "select",
        file: null,
        description: "",
        selectFile: null,
      },
    });

  const disabledSubmit =
    watch("radio") === "upload" ? !watch("file") : !watch("selectFile");

  const handleCancelUpload = () => {
    if (cancelTokenRef.current) {
      cancelTokenRef.current.abort();
      setIsLoading(false);
    }
  };

  const onSubmit = async (data: FormValues) => {
    const { radio, file, selectFile, description } = data;

    const formData = new FormData();

    if (radio === "upload" && file) formData.append("file", data.file as File);

    if (radio === "select" && selectFile)
      formData.append("resume_id", selectFile.id);

    if (data.description) formData.append("jobDescription", description);

    await mutateAsync(formData);
  };

  const onOptimize = () => {
    const { file, selectFile, description, radio } = getValues();
    onNavigateToOptimize({
      resume: radio === "select" ? selectFile : null,
      file: radio === "upload" ? file : null,
      jobDescription: description,
      radio: radio,
      evaluateData: evaluate,
    });
    route.push(ROUTE.OPTIMIZE_RESUME);
  };

  const parseFeedback = (data: string) => {
    if (!data) return;

    const sections = data.split("\n\n");
    const result = {
      ["General Feedback"]: "",
      ["Job Match Feedback"]: "",
      ["Key recommendations to improve job match"]: "",
    };

    sections.forEach((section) => {
      if (section.startsWith("General Feedback:")) {
        result["General Feedback"] = section
          .replace("General Feedback:", "")
          .trim();
      } else if (section.startsWith("Job Match Feedback:")) {
        result["Job Match Feedback"] = section
          .replace("Job Match Feedback:", "")
          .trim();
      } else if (
        section.startsWith("Key recommendations to improve job match:")
      ) {
        result["Key recommendations to improve job match"] = section
          .replace("Key recommendations to improve job match:", "")
          .trim();
      }
    });

    return result;
  };

  const overallFeedback = parseFeedback(
    evaluate?.detailedEvaluation?.overall_summary as string
  );

  useEffect(() => {
    if (navigateToScore) {
      reset({
        ...watch(),
        selectFile: navigateToScore.resume,
        radio: "select",
      });
      onResetNavigateToScore();
    }
    return () => {
      onChangeHeader({ fileName: "" });
      setCallback(null);
    };
  }, []);

  if (evaluate && isSuccess && header.fileName) {
    return (
      <div className="bg-[#F8F8F8] flex justify-center">
        <div className="max-w-[calc(1440px-18rem)] flex flex-col gap-2">
          <div className="flex min-[1024px]:hidden flex-col gap-2 px-6 py-2">
            <div
              className="flex gap-1 items-center cursor-pointer w-fit"
              onClick={onBack}
            >
              <CircleChevronLeft fill="#667085" color="#fff" />
              <p className="text-base text-[#667085]">Back to scan</p>
            </div>

            <h4 className="text-xl font-semibold text-[#06042B]">
              {header.fileName}
            </h4>
          </div>

          <div className="w-full flex justify-center mx-0 md:px-4 pb-6 min-[1024px]:py-6">
            <div className="max-w-[calc(1440px-18rem)] flex flex-col md:flex-row h-full">
              <div className="w-full md:w-[290px] p-6 border-none md:border-r-4 md:border-[#ededed] bg-background">
                <div className="flex flex-col gap-4 items-center pb-6 border-b-[1px] border-[#F3F3F3]">
                  <p className="text-lg font-semibold">Overall Score</p>
                  <CircleProgress
                    percent={evaluate?.detailedEvaluation.combined_total_score}
                  />
                  <Button className="bg-[#17B530] hover:bg-[#17B530] py-2 px-3 w-fit">
                    Recommended
                  </Button>
                </div>

                <div
                  className={cn(
                    "flex flex-col gap-4 py-6",
                    watch("description")
                      ? "border-b-[1px] border-[#F3F3F3]"
                      : ""
                  )}
                >
                  <p className={cn("text-lg font-semibold", SFPro.className)}>
                    General CV Quality Scores
                  </p>

                  <div className="flex flex-col gap-5">
                    <div>
                      <p className="text-xs text-[#667085] font-semibold">
                        Impact
                      </p>
                      <Progress
                        value={
                          evaluate?.detailedEvaluation?.general_assessment
                            ?.scores?.["impact"]?.score
                        }
                        className="h-2"
                      />
                    </div>

                    <div>
                      <p className="text-xs text-[#667085] font-semibold">
                        Brevity
                      </p>
                      <Progress
                        value={
                          evaluate?.detailedEvaluation?.general_assessment
                            ?.scores?.["brevity"]?.score
                        }
                        className="h-2"
                      />
                    </div>

                    <div>
                      <p className="text-xs text-[#667085] font-semibold">
                        Style
                      </p>
                      <Progress
                        value={
                          evaluate?.detailedEvaluation?.general_assessment
                            ?.scores?.["style"]?.score
                        }
                        className="h-2"
                      />
                    </div>

                    <div>
                      <p className="text-xs text-[#667085] font-semibold">
                        Spelling Grammar
                      </p>
                      <Progress
                        value={
                          evaluate?.detailedEvaluation?.general_assessment
                            ?.scores?.["spelling_grammar"]?.score
                        }
                        className="h-2"
                      />
                    </div>

                    <div>
                      <p className="text-xs text-[#667085] font-semibold">
                        Contact Information
                      </p>
                      <Progress
                        value={
                          evaluate?.detailedEvaluation?.general_assessment
                            ?.scores?.["contact_information"]?.score
                        }
                        className="h-2"
                      />
                    </div>
                  </div>
                </div>

                <div
                  className={cn(
                    "flex flex-col gap-4 py-6",
                    watch("description") ? "flex" : "hidden"
                  )}
                >
                  <p className={cn("text-lg font-semibold", SFPro.className)}>
                    Job Match Scores
                  </p>

                  <div className="flex flex-col gap-5">
                    <div>
                      <p className="text-xs text-[#667085] font-semibold">
                        Skills match
                      </p>
                      <Progress
                        value={
                          evaluate?.detailedEvaluation?.job_match_assessment
                            ?.scores?.["skills_match"]?.score
                        }
                        className="h-2"
                      />
                    </div>

                    <div>
                      <p className="text-xs text-[#667085] font-semibold">
                        Relevant keywords
                      </p>
                      <Progress
                        value={
                          evaluate?.detailedEvaluation?.job_match_assessment
                            ?.scores?.["relevant_keywords"]?.score
                        }
                        className="h-2"
                      />
                    </div>

                    <div>
                      <p className="text-xs text-[#667085] font-semibold">
                        Experience Alignment
                      </p>
                      <Progress
                        value={
                          evaluate?.detailedEvaluation?.job_match_assessment
                            ?.scores?.["experience_alignment"]?.score
                        }
                        className="h-2"
                      />
                    </div>
                  </div>
                </div>

                <div className="w-full flex justify-center">
                  <Button className="md:w-full" onClick={onOptimize}>
                    Optimize Now
                  </Button>
                </div>
              </div>

              <div className="flex-1 bg-[#edecfe] md:bg-[#f9f9ff] p-4 md:p-8">
                <div className="pb-4 flex flex-col gap-3">
                  <p className="text-lg text-[#06042B] font-semibold">
                    General Information
                  </p>
                  <div
                    className={cn(
                      "bg-background rounded-xl",
                      watch("description") ? "block" : "hidden"
                    )}
                  >
                    {overallFeedback &&
                      Object.entries(overallFeedback).map(
                        ([key, value], index) => (
                          <p
                            key={key}
                            className={cn(
                              "text-sm leading-6 py-3 px-4 sm:px-6",
                              index ===
                                Object.entries(overallFeedback).length - 1
                                ? ""
                                : "border-b-[1px] border-[#ECECEC]"
                            )}
                          >
                            <span className="font-semibold">{key}:</span>{" "}
                            {key ===
                            "Key recommendations to improve job match" ? (
                              <br />
                            ) : null}
                            <span className="text-[#667085]">
                              {value.split("\n").map((line, index) => (
                                <React.Fragment key={index}>
                                  {line}
                                  {index < value.split("\n").length - 1 && (
                                    <br />
                                  )}{" "}
                                </React.Fragment>
                              ))}
                            </span>
                          </p>
                        )
                      )}
                  </div>

                  <div
                    className={cn(
                      "bg-background rounded-xl",
                      watch("description") ? "hidden" : "block"
                    )}
                  >
                    <p className="text-sm leading-6 py-3 px-4 sm:px-6 border-b-[1px] border-[#ECECEC]">
                      <span className="font-semibold">General Feedback:</span>
                      <span className="text-[#667085]">
                        {evaluate.detailedEvaluation.overall_summary}
                      </span>
                    </p>
                  </div>
                </div>

                <div className="grid grid-cols-1 xl:grid-cols-2">
                  <div className="col-span-1 p-4 md:p-8 rounded-ss-xl rounded-se-xl bg-[#EBFFEE]">
                    <p className="text-lg text-[#06042B]">Key Strenghs</p>

                    <ul className="list-none space-y-4 text-sm">
                      {evaluate?.detailedEvaluation.key_strengths &&
                      evaluate?.detailedEvaluation.key_strengths.length > 0 ? (
                        evaluate?.detailedEvaluation.key_strengths.map(
                          (strength: any, index: number) => (
                            <>
                              <div className="flex items-start gap-1">
                                <div className="w-6">
                                  <CircleCheck
                                    size={24}
                                    fill="#17B530"
                                    color="#fff"
                                  />
                                </div>
                                <li key={index}>
                                  <span className="font-medium">
                                    {strength.criterion}
                                  </span>
                                  : {strength.description}
                                </li>
                              </div>

                              <ul className="list-disc ml-6 space-y-2 text-sm">
                                {strength?.suggestions?.length
                                  ? strength?.suggestions.map(
                                      (item: string) => (
                                        <li
                                          key={item}
                                          className="text-[#667085]"
                                        >
                                          {item}
                                        </li>
                                      )
                                    )
                                  : null}
                              </ul>
                            </>
                          )
                        )
                      ) : (
                        <li>
                          No particular strengths identified. Focus on improving
                          your CV using the suggestions provided.
                        </li>
                      )}
                    </ul>
                  </div>

                  <div className="col-span-1 p-4 md:p-8 rounded-ee-xl rounded-es-xl bg-[#FFF9EB]">
                    <p className="text-lg text-[#06042B]">Areas to Improve</p>

                    <ul className="list-none space-y-4 text-sm">
                      {evaluate?.detailedEvaluation.improvement_areas &&
                      evaluate?.detailedEvaluation.improvement_areas.length >
                        0 ? (
                        evaluate?.detailedEvaluation.improvement_areas.map(
                          (strength: any, index: number) => (
                            <>
                              <div className="flex items-center">
                                <div className="w-6">
                                  <AlertTriangle
                                    fill="#FFBE18"
                                    color="#fff"
                                    size={24}
                                  />
                                </div>
                                <li key={index}>
                                  <span className="font-medium">
                                    {strength.criterion}
                                  </span>
                                  : {extractScoreString(strength.description)}
                                </li>
                              </div>
                              <ul className="list-disc ml-6 space-y-2 text-sm">
                                {strength?.suggestions?.length
                                  ? strength?.suggestions.map(
                                      (item: string) => (
                                        <li
                                          key={item}
                                          className="text-[#667085]"
                                        >
                                          {item}
                                        </li>
                                      )
                                    )
                                  : null}
                              </ul>
                            </>
                          )
                        )
                      ) : (
                        <li>
                          No particular strengths identified. Focus on improving
                          your CV using the suggestions provided.
                        </li>
                      )}
                    </ul>
                  </div>
                </div>

                <div className="flex flex-col gap-2 pt-8">
                  <p className="text-lg text-[#06042B] font-semibold">
                    Detailed Evaluation
                  </p>

                  <Tabs
                    defaultValue="general"
                    className="bg-background rounded-xl"
                  >
                    <TabsList className="w-full bg-background rounded-none rounded-ss-xl rounded-se-xl p-0 h-auto">
                      <TabsTrigger
                        value="general"
                        className={cn(
                          "w-full bg-transparent rounded-none rounded-ss-xl data-[state=active]:bg-[#F1F1F1] data-[state=active]:shadow-none",
                          watch("description")
                            ? "data-[state=active]:border-t-2 data-[state=active]:border-main"
                            : ""
                        )}
                      >
                        General Assessment
                      </TabsTrigger>
                      <TabsTrigger
                        value="job"
                        className={cn(
                          "w-full bg-transparent rounded-none rounded-se-xl data-[state=active]:bg-[#F1F1F1] data-[state=active]:border-t-2 data-[state=active]:border-main data-[state=active]:shadow-none",
                          watch("description") ? "block" : "hidden"
                        )}
                      >
                        Job Match Assessment
                      </TabsTrigger>
                    </TabsList>
                    <TabsContent value="general" className="pt-0 md:pt-5">
                      <div className="">
                        {Object.entries(
                          evaluate?.detailedEvaluation?.detailed_scores || {}
                        )
                          .filter(
                            ([key, value]: [string, any]) =>
                              value.category === "General"
                          )
                          .map(([key, value]: [string, any], index, array) => (
                            <div
                              key={key}
                              className={cn(
                                "px-4 md:px-8 py-5",
                                index === array.length - 1
                                  ? ""
                                  : "border-b-2 border-[#EDEDED]"
                              )}
                            >
                              <div className="flex gap-2 items-center">
                                <p
                                  className={cn(
                                    getColorByScore(value.score),
                                    "border border-[#CBCBCB] px-2 py-1 rounded-md text-sm font-semibold"
                                  )}
                                >
                                  {value.score}%
                                </p>
                                <div className="font-medium">
                                  {snakeToTitleCase(value.criterion || key)}
                                </div>
                              </div>

                              {value.strengths &&
                                value.strengths.length > 0 && (
                                  <div className="mt-3 mb-2">
                                    <div className="flex items-center gap-1">
                                      <Image alt="" src={StrengthsIcon} />
                                      <p className="text-sm font-medium text-gray-900 dark:text-gray-100">
                                        Strengths:
                                      </p>
                                    </div>
                                    <ul className="list-disc ml-5 text-sm text-gray-600 dark:text-gray-300 pt-1">
                                      {value.strengths.map(
                                        (strength: string, i: number) => (
                                          <li key={i}>{strength}</li>
                                        )
                                      )}
                                    </ul>
                                  </div>
                                )}

                              <div className="mt-3">
                                <div className="flex items-center gap-1">
                                  <Image alt="" src={FeedbackIcon} />
                                  <p className="text-sm font-medium text-gray-900 dark:text-gray-100">
                                    Feedback:
                                  </p>
                                </div>
                                <p className="text-sm text-gray-600 dark:text-gray-300 pt-1">
                                  {value.feedback}
                                </p>
                              </div>
                            </div>
                          ))}
                      </div>
                    </TabsContent>
                    <TabsContent value="job" className="pt-5">
                      {evaluate?.detailedEvaluation.industry_recommendation && (
                        <div className="px-8 border-b-2 border-[#EDEDED]">
                          <div className="mb-4 p-4 bg-[#EDECFE] rounded-xl">
                            <h4 className="font-bold text-md mb-2">
                              Industry Detected:{" "}
                              {evaluate?.detailedEvaluation.industry_recommendation.industry.toUpperCase()}
                            </h4>
                            <div className="text-sm text-[#667085] leading-6">
                              <p>
                                Based on your job description, we've detected
                                you're applying for a position in the{" "}
                                {
                                  evaluate?.detailedEvaluation
                                    .industry_recommendation.industry
                                }{" "}
                                industry.
                              </p>
                              <p>Industry-specific recommendations:</p>
                              <ul className="list-disc ml-5 space-y-1">
                                {evaluate?.detailedEvaluation.industry_recommendation.recommendations.map(
                                  (rec: string, i: number) => (
                                    <li key={i}>{rec}</li>
                                  )
                                )}
                              </ul>
                            </div>
                          </div>
                        </div>
                      )}

                      <div className="space-y-4">
                        {Object.entries(
                          evaluate?.detailedEvaluation.detailed_scores || {}
                        )
                          .filter(
                            ([key, value]: [string, any]) =>
                              value.category === "Job Match"
                          )
                          .map(([key, value]: [string, any], index, array) => (
                            <div
                              key={key}
                              className={cn(
                                "px-8 py-5",
                                index === array.length - 1
                                  ? ""
                                  : "border-b-2 border-[#EDEDED]"
                              )}
                            >
                              <div className="font-medium">
                                {snakeToTitleCase(value.criterion || key)}
                              </div>

                              {value.matching_items &&
                                value.matching_items.length > 0 && (
                                  <div className="mt-3 mb-2">
                                    <div className="flex items-center gap-1">
                                      <Image alt="" src={MatchingIcon} />
                                      <p className="text-sm font-medium">
                                        Matching Items:
                                      </p>
                                    </div>
                                    <div className="flex flex-wrap gap-1 pt-1">
                                      {value.matching_items.map(
                                        (item: string, i: number) => (
                                          <Badge
                                            key={i}
                                            variant="outline"
                                            className="rounded-md border-[#6BAF76] bg-[#D8F5D9] text-sm font-normal"
                                          >
                                            {item}
                                          </Badge>
                                        )
                                      )}
                                    </div>
                                  </div>
                                )}

                              {value.missing_items &&
                                value.missing_items.length > 0 && (
                                  <div className="mt-3 mb-2">
                                    <div className="flex items-center gap-1">
                                      <Image alt="" src={MissingIcon} />
                                      <p className="text-sm font-medium">
                                        Missing Items:
                                      </p>
                                    </div>
                                    <div className="flex flex-wrap gap-1 pt-1">
                                      {value.missing_items.map(
                                        (item: string, i: number) => (
                                          <Badge
                                            key={i}
                                            variant="outline"
                                            className="rounded-md bg-[#FDF3D8] border-[#E9AE0C] text-sm font-normal"
                                          >
                                            {item}
                                          </Badge>
                                        )
                                      )}
                                    </div>
                                  </div>
                                )}

                              <div className="mt-3">
                                <div className="text-sm font-medium text-gray-900 dark:text-gray-100 pt-1">
                                  Feedback:
                                </div>
                                <p className="text-sm text-gray-600 dark:text-gray-300">
                                  {value.feedback}
                                </p>
                              </div>
                            </div>
                          ))}
                      </div>
                    </TabsContent>
                  </Tabs>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="py-6 px-4 sm:px-6 flex flex-col items-center">
      {isLoading && (
        <LoadingOverlay
          messages={MESSAGES_LOADING_EVALUATE_RESUME}
          onCancel={handleCancelUpload}
        />
      )}
      <div
        className={cn(
          "w-full md:w-[730px]",
          isLoading ? "pointer-events-none" : "pointer-events-auto"
        )}
      >
        <h3 className="text-2xl font-semibold text-center">AI Resume Score</h3>
        <p className="text-base text-[#667085] text-center pt-2">
          Upload your CV and job description to get personalized feedback and
          scoring
        </p>

        <form onSubmit={handleSubmit(onSubmit)} className="w-full pt-3">
          <Controller
            name="radio"
            control={control}
            render={({ field }) => (
              <RadioGroup
                value={field.value}
                onValueChange={field.onChange}
                className="flex items-center gap-4 pt-4"
              >
                {RADIO_BUTTON.map((item) => (
                  <div className="flex items-center space-x-2" key={item.value}>
                    <RadioGroupItem
                      value={item.value}
                      id={item.value}
                      checked={field.value === item.value}
                    />
                    <Label
                      htmlFor={item.value}
                      className="text-sm md:text-base font-normal"
                    >
                      {item.label}
                    </Label>
                  </div>
                ))}
              </RadioGroup>
            )}
          />

          <div
            className={cn(
              "pt-3",
              watch("radio") === "upload" ? "block" : "hidden"
            )}
          >
            <Controller
              name="file"
              control={control}
              render={({ field: { value, onChange } }) => (
                <FileUpload
                  file={value}
                  onFileSelect={(file) => onChange(file)}
                />
              )}
            />
          </div>

          <div
            className={cn(
              "pt-3",
              watch("radio") === "select" ? "block" : "hidden"
            )}
          >
            <Controller
              name="selectFile"
              control={control}
              render={({ field: { value, onChange } }) => (
                <ComboboxResume
                  resume={value}
                  onChange={(resume) => onChange(resume)}
                />
              )}
            />
          </div>

          <div className="pt-5 flex flex-col gap-2">
            <Label htmlFor="description" className="text-base font-medium">
              Job Description
            </Label>
            <Controller
              name="description"
              control={control}
              render={({ field }) => (
                <textarea
                  id="description"
                  {...field}
                  placeholder="Enter your description here"
                  className="w-full h-64 p-3 border border-[#E7E7E7] rounded-lg focus:outline-none focus:ring-1 focus:ring-main focus:border-main resize-none"
                />
              )}
            />
          </div>

          <div className="pt-5">
            <Button
              type="submit"
              disabled={disabledSubmit || isLoading}
              className="w-full px-4 py-5"
            >
              {isLoading ? "Scoring..." : "Score Resume"}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default AIResumeScore;
