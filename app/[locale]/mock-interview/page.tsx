"use client";

import { Phone, Mic, MicOff, Volume2, VolumeX } from "lucide-react";
import { useState, useEffect, useRef } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { ScrollArea } from "@/components/ui/scroll-area";
import { INITIAL_PROMPT, FOLLOW_UP_PROMPT, getAIResponse, InterviewState } from "@/lib/interview";
import { AssemblyAI } from 'assemblyai';

// Khởi tạo AssemblyAI client
const assemblyClient = new AssemblyAI({
  apiKey: "********************************",
});

// ElevenLabs API key
const ELEVENLABS_API_KEY = "***************************************************";
// Sử dụng Rachel voice ID (giọng nữ chuyên nghiệp)
const ELEVENLABS_VOICE_ID = "21m00Tcm4TlvDq8ikWAM";
// ElevenLabs API URL
const ELEVENLABS_API_URL = "https://api.elevenlabs.io/v1";

// Type cho audio recorder
interface AudioRecorder {
  start: () => void;
  stop: () => Promise<Blob>;
  isRecording: boolean;
}

// Type cho micStatus
type MicStatus = 'inactive' | 'recording' | 'processing';

export default function MockInterview() {
  const [micStatus, setMicStatus] = useState<MicStatus>('inactive');
  const [messages, setMessages] = useState<Array<{ text: string; isUser: boolean }>>([]);
  const [interviewState, setInterviewState] = useState<InterviewState>({
    questionCount: 0,
    isFinished: false,
    started: false
  });
  const [isLoading, setIsLoading] = useState(false);
  const [isSpeaking, setIsSpeaking] = useState(false);
  const audioRecorderRef = useRef<AudioRecorder | null>(null);
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const audioChunksRef = useRef<Blob[]>([]);
  const audioPlayerRef = useRef<HTMLAudioElement | null>(null);

  useEffect(() => {
    // Khởi tạo audio recorder
    if (typeof window !== "undefined") {
      // Tạo audio recorder để ghi âm từ microphone
      const setupAudioRecorder = async (): Promise<AudioRecorder> => {
        // Yêu cầu quyền truy cập microphone với chất lượng cao hơn
        const stream = await navigator.mediaDevices.getUserMedia({
          audio: {
            echoCancellation: true,
            noiseSuppression: true,
            autoGainControl: true,
            sampleRate: 44100
          }
        });
        
        console.log('Audio stream obtained:', stream.getAudioTracks()[0].label);
        console.log('Audio settings:', stream.getAudioTracks()[0].getSettings());
        
        mediaRecorderRef.current = new MediaRecorder(stream, {
          mimeType: 'audio/webm;codecs=opus', // Sử dụng định dạng được hỗ trợ rộng rãi
          audioBitsPerSecond: 128000 // 128kbps cho chất lượng tốt
        });
        
        console.log('MediaRecorder created with mimeType:', mediaRecorderRef.current.mimeType);
        
        mediaRecorderRef.current.ondataavailable = (event) => {
          console.log('Data available:', event.data.size, 'bytes');
          if (event.data.size > 0) {
            audioChunksRef.current.push(event.data);
          }
        };
        
        return {
          start: () => {
            console.log('Starting audio recording...');
            audioChunksRef.current = [];
            mediaRecorderRef.current?.start(250); // Ghi âm với khoảng thời gian 250ms
          },
          stop: () => {
            return new Promise<Blob>((resolve) => {
              if (!mediaRecorderRef.current) {
                console.error('MediaRecorder is not initialized');
                resolve(new Blob());
                return;
              }
              
              mediaRecorderRef.current.onstop = () => {
                console.log('Recording stopped, creating blob from', audioChunksRef.current.length, 'chunks');
                // Sử dụng định dạng tương thích với AssemblyAI
                const audioBlob = new Blob(audioChunksRef.current, { type: mediaRecorderRef.current?.mimeType || 'audio/webm' });
                console.log('Created blob of size:', audioBlob.size, 'bytes');
                resolve(audioBlob);
              };
              
              console.log('Stopping MediaRecorder...');
              mediaRecorderRef.current.stop();
            });
          },
          isRecording: !!mediaRecorderRef.current && mediaRecorderRef.current.state === 'recording'
        };
      };
      
      setupAudioRecorder().then(recorder => {
        audioRecorderRef.current = recorder;
      }).catch(error => {
        console.error('Error setting up audio recorder:', error);
      });
    }
    
    // Cleanup khi component unmount
    return () => {
      if (mediaRecorderRef.current && mediaRecorderRef.current.state === 'recording') {
        mediaRecorderRef.current.stop();
      }
      
      // Dọn dẹp các track của MediaStream
      if (mediaRecorderRef.current && mediaRecorderRef.current.stream) {
        const tracks = mediaRecorderRef.current.stream.getTracks();
        tracks.forEach(track => track.stop());
      }
    };
  }, []);

  // Sử dụng ElevenLabs để đọc văn bản thành giọng nói
  const speakWithElevenLabs = async (text: string) => {
    try {
      setIsSpeaking(true);
      console.log('Generating audio with ElevenLabs:', text.substring(0, 50) + '...');

      // Gọi trực tiếp ElevenLabs API thông qua fetch
      const response = await fetch(
        `${ELEVENLABS_API_URL}/text-to-speech/${ELEVENLABS_VOICE_ID}`,
        {
          method: 'POST',
          headers: {
            'Accept': 'audio/mpeg',
            'Content-Type': 'application/json',
            'xi-api-key': ELEVENLABS_API_KEY,
          },
          body: JSON.stringify({
            text: text,
            model_id: 'eleven_multilingual_v2',
            voice_settings: {
              stability: 0.5,
              similarity_boost: 0.8,
              style: 0.5,
            },
          }),
        }
      );

      if (!response.ok) {
        throw new Error(`ElevenLabs API error: ${response.status} ${response.statusText}`);
      }

      // Lấy dữ liệu audio dạng ArrayBuffer
      const audioArrayBuffer = await response.arrayBuffer();
      console.log('Received audio data:', audioArrayBuffer.byteLength, 'bytes');
      
      // Chuyển đổi ArrayBuffer thành Blob
      const audioBlob = new Blob([audioArrayBuffer], { type: 'audio/mpeg' });
      const audioUrl = URL.createObjectURL(audioBlob);
      
      // Phát audio
      if (!audioPlayerRef.current) {
        audioPlayerRef.current = new Audio();
      }
      
      audioPlayerRef.current.src = audioUrl;
      audioPlayerRef.current.onended = () => {
        setIsSpeaking(false);
        URL.revokeObjectURL(audioUrl); // Giải phóng bộ nhớ
      };
      
      await audioPlayerRef.current.play();
      
    } catch (error) {
      console.error('Error generating audio with ElevenLabs:', error);
      // Fallback to browser's speech synthesis if ElevenLabs fails
      const speech = new SpeechSynthesisUtterance(text);
      speech.lang = "en-US";
      speech.onend = () => setIsSpeaking(false);
      window.speechSynthesis.speak(speech);
    }
  };
  
  // Dừng phát âm thanh
  const stopSpeaking = () => {
    if (audioPlayerRef.current) {
      audioPlayerRef.current.pause();
      audioPlayerRef.current.currentTime = 0;
    }
    window.speechSynthesis.cancel();
    setIsSpeaking(false);
  };

  const startInterview = async () => {
    setIsLoading(true);
    setMessages([]);
    const aiMessages = [
      { role: "system", content: INITIAL_PROMPT },
    ];
    
    const response = await getAIResponse(aiMessages);
    
    // Sử dụng ElevenLabs để đọc câu hỏi đầu tiên
    await speakWithElevenLabs(response);
    
    setMessages([{ text: response, isUser: false }]);
    setInterviewState({ questionCount: 1, isFinished: false, started: true });
    setIsLoading(false);
  };

  // Xử lý âm thanh và chuyển đổi thành văn bản sử dụng AssemblyAI
  const processAudio = async (audioBlob: Blob): Promise<string> => {
    try {
      console.log('Processing audio, blob size:', audioBlob.size, 'bytes');
      if (audioBlob.size === 0) {
        console.error('Audio blob is empty!');
        return '';
      }
      
      // Tạo một File từ Blob để có thể gửi đến AssemblyAI
      const audioFile = new File([audioBlob], 'audio-recording.wav', { type: 'audio/wav' });
      console.log('Created audio file:', audioFile.name, audioFile.size, 'bytes');
      
      // Debug: Hiển thị một đoạn audio để kiểm tra
      const audioUrl = URL.createObjectURL(audioBlob);
      console.log('Audio preview URL:', audioUrl);
      
      // Thêm audio element tạm thời để kiểm tra
      const tempAudio = document.createElement('audio');
      tempAudio.src = audioUrl;
      tempAudio.controls = true;
      tempAudio.style.display = 'none';
      document.body.appendChild(tempAudio);
      
      // Gửi và xử lý với AssemblyAI
      console.log('Sending to AssemblyAI...');
      const transcript = await assemblyClient.transcripts.transcribe({
        audio: audioFile,
        language_code: 'en' // Thay đổi thành 'vi' nếu cần tiếng Việt
      });
      
      console.log('Received transcript response:', transcript);
      document.body.removeChild(tempAudio);
      return transcript.text || '';
    } catch (error) {
      console.error('Error transcribing audio:', error);
      return '';
    }
  };

  const handleUserMessage = async (userMessage: string) => {
    if (interviewState.isFinished || !userMessage.trim()) return;
    
    setMessages(prev => [...prev, { text: userMessage, isUser: true }]);

    // Prepare messages for AI
    const aiMessages = [
      { role: "system", content: INITIAL_PROMPT },
      ...messages.map((msg, index) => ({
        role: index % 2 === 0 ? "assistant" : "user",
        content: msg.text
      })),
      { role: "user", content: userMessage }
    ];

    // Add follow-up system message
    aiMessages.push({ role: "system", content: FOLLOW_UP_PROMPT(aiMessages) });

    setIsLoading(true);
    const botResponse = await getAIResponse(aiMessages);
    setIsLoading(false);

    // Sử dụng ElevenLabs để đọc phản hồi
    await speakWithElevenLabs(botResponse);

    // Update messages and interview state
    setMessages(prev => [...prev, { text: botResponse, isUser: false }]);
    
    const newQuestionCount = (messages.length + 2) / 2;
    setInterviewState({
      questionCount: newQuestionCount,
      isFinished: newQuestionCount >= 5,
      started: true
    });
  };

  const toggleRecording = async () => {
    if (!audioRecorderRef.current) return;

    if (micStatus === 'recording') {
      // Dừng ghi âm và xử lý
      setMicStatus('processing');
      try {
        const audioBlob = await audioRecorderRef.current.stop();
        const transcription = await processAudio(audioBlob);
        if (transcription.trim()) {
          handleUserMessage(transcription);
        }
      } catch (error) {
        console.error('Error processing audio:', error);
      } finally {
        setMicStatus('inactive');
      }
    } else if (micStatus === 'inactive') {
      // Bắt đầu ghi âm mới
      setMicStatus('recording');
      audioRecorderRef.current.start();
    }
    // Nếu đang xử lý (processing), không làm gì
  };

  return (
    <div className="min-h-screen bg-gradient-to-b from-gray-100 to-gray-200 p-4">
      <div className="max-w-md mx-auto">
        <Card className="p-4 shadow-xl">
          <div className="mb-4 text-center">
            <h1 className="text-2xl font-bold text-gray-800">AI Technical Interview</h1>
            {interviewState.started ? (
              <p className="text-sm text-gray-600">
                {interviewState.isFinished 
                  ? "Interview Complete" 
                  : `Question ${interviewState.questionCount} of 5`}
              </p>
            ) : (
              <p className="text-sm text-gray-600">Click Start Interview to begin</p>
            )}
          </div>
          
          <ScrollArea className="h-[400px] mb-4 p-4">
            {messages.map((message, index) => (
              <div
                key={index}
                className={`mb-2 p-3 rounded-lg ${
                  message.isUser
                    ? "bg-blue-500 text-white ml-auto"
                    : "bg-gray-200 text-gray-800"
                } max-w-[80%] ${message.isUser ? "ml-auto" : "mr-auto"}`}
              >
                {message.text}
              </div>
            ))}
            {isLoading && (
              <div className="text-center text-gray-500">
                Processing response...
              </div>
            )}
          </ScrollArea>
          
          <div className="flex flex-col items-center gap-4">
            {!interviewState.started ? (
              <Button 
                onClick={startInterview}
                className="bg-green-500 hover:bg-green-600 px-8 py-4 text-lg"
                disabled={isLoading}
              >
                Start Interview
              </Button>
            ) : (
              <div className="flex gap-2">
                <Button
                  onClick={toggleRecording}
                  disabled={isLoading || interviewState.isFinished || micStatus === 'processing'}
                  className={`rounded-full p-6 ${
                    micStatus === 'recording' ? "bg-red-500 hover:bg-red-600" : "bg-blue-500 hover:bg-blue-600"
                  } ${(isLoading || interviewState.isFinished || micStatus === 'processing') ? "opacity-50 cursor-not-allowed" : ""}`}
                >
                  {micStatus === 'recording' ? (
                    <MicOff className="h-6 w-6 animate-pulse" />
                  ) : micStatus === 'processing' ? (
                    <div className="h-6 w-6 animate-spin rounded-full border-2 border-white border-opacity-20 border-t-white"></div>
                  ) : (
                    <Mic className="h-6 w-6" />
                  )}
                </Button>
                
                {isSpeaking && (
                  <Button 
                    onClick={stopSpeaking} 
                    className="rounded-full p-6 bg-purple-500 hover:bg-purple-600"
                  >
                    <VolumeX className="h-6 w-6" />
                  </Button>
                )}
              </div>
            )}
            
            {interviewState.isFinished && (
              <Button 
                onClick={startInterview}
                className="bg-green-500 hover:bg-green-600"
              >
                Start New Interview
              </Button>
            )}
          </div>
        </Card>
      </div>
    </div>
  );
}
