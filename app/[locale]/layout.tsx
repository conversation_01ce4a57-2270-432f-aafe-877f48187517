import "../globals.css";
import { GoogleAnalytics } from "@next/third-parties/google";
import { ThemeProvider } from "@/providers/theme-provider";
import { QueryProvider } from "@/providers/query-provider";
import { AuthProvider } from "@/providers/auth-provider";
import { PendoProvider } from "@/providers/pendo-provider";
import { NextIntlClientProvider, hasLocale } from "next-intl";
import { routing } from "@/i18n/routing";
import { notFound } from "next/navigation";
import { setRequestLocale } from "next-intl/server";
import {
  inter,
  roboto,
  openSans,
  montserrat,
  lato,
  poppins,
  SFPro,
} from "@/app/fonts";
import Script from "next/script";
import Layout from "@/components/common/layouts";
import { Toaster } from "@/components/ui/toaster";

// Client component for detecting paths

export default async function RootLayout({
  children,
  params,
}: {
  children: React.ReactNode;
  params: Promise<{ locale: string }>;
}) {
  const { locale } = await params;
  if (!hasLocale(routing.locales, locale)) {
    notFound();
  } else {
    setRequestLocale(locale);
  }

  return (
    <html lang={locale} suppressHydrationWarning>
      <head>
        <Script
          src={`
            https://cdn.pendo.io/agent/static/${process.env.NEXT_PUBLIC_PENDO_KEY}/pendo.js
          `}
          strategy="beforeInteractive"
        />
      </head>
      <body
        className={`${SFPro.className} ${inter.className} ${roboto.className} ${openSans.className} ${montserrat.className} ${lato.className} ${poppins.className}`}
      >
        <NextIntlClientProvider>
          <ThemeProvider
            attribute="class"
            defaultTheme="light"
            enableColorScheme={false}
          >
            <QueryProvider>
              <AuthProvider>
                <PendoProvider>
                  <Layout locale={locale}>{children}</Layout>
                  <Toaster />
                </PendoProvider>
              </AuthProvider>
            </QueryProvider>
          </ThemeProvider>
        </NextIntlClientProvider>
        <GoogleAnalytics gaId="G-BSQX2L2LZQ" />
      </body>
    </html>
  );
}
