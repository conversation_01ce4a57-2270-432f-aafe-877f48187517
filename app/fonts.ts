import {
  <PERSON>,
  <PERSON><PERSON>,
  Open_Sans,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
} from "next/font/google";
import localFont from "next/font/local";

export const inter = Inter({ subsets: ["latin"], variable: "--font-inter" });

export const roboto = Roboto({
  weight: ["400", "500", "700"],
  subsets: ["latin"],
  variable: "--font-roboto",
});

export const openSans = Open_Sans({
  weight: ["400", "600", "700"],
  subsets: ["latin"],
  variable: "--font-open-sans",
});

export const montserrat = Montserrat({
  weight: ["400", "500", "600", "700"],
  subsets: ["latin"],
  variable: "--font-montserrat",
});

export const lato = Lato({
  weight: ["400", "700"],
  subsets: ["latin"],
  variable: "--font-lato",
});

export const poppins = Poppins({
  weight: ["400", "500", "600", "700"],
  subsets: ["latin"],
  variable: "--font-poppins",
});

export const SFPro = localFont({
  src: [
    { path: "../public/fonts/SFPRODISPLAYMEDIUM.otf", weight: "500" },
    { path: "../public/fonts/SFPRODISPLAYREGULAR.otf", weight: "400" },
  ],
  variable: "--font-sfpro",
});

// Danh sách tất cả các font có sẵn cho editor
export const availableFonts = [
  { name: "Inter", value: "var(--font-inter)" },
  { name: "Roboto", value: "var(--font-roboto)" },
  { name: "Open Sans", value: "var(--font-open-sans)" },
  { name: "Montserrat", value: "var(--font-montserrat)" },
  { name: "Lato", value: "var(--font-lato)" },
  { name: "Poppins", value: "var(--font-poppins)" },
  { name: "SF Pro", value: "var(--font-sfpro)" },
];
